import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../service/payment_service.dart';

class EnhancedPaymentDialog extends StatefulWidget {
  final String qrCode;
  final List<dynamic> bankUrls;
  final String? invoiceId;
  final VoidCallback? onPaymentSuccess;
  final VoidCallback? onClose;

  const EnhancedPaymentDialog({
    Key? key,
    required this.qrCode,
    required this.bankUrls,
    this.invoiceId,
    this.onPaymentSuccess,
    this.onClose,
  }) : super(key: key);

  @override
  State<EnhancedPaymentDialog> createState() => _EnhancedPaymentDialogState();
}

class _EnhancedPaymentDialogState extends State<EnhancedPaymentDialog> {
  final PaymentService _paymentService = PaymentService();
  PaymentStatus _paymentStatus = PaymentStatus.idle;
  double _paymentProgress = 0.0;
  String _paymentMessage = '';

  @override
  void initState() {
    super.initState();
    _setupPaymentListeners();
    
    // Start automatic payment checking if invoice ID is provided
    if (widget.invoiceId != null) {
      _paymentService.startPaymentCheck(widget.invoiceId!);
    }
  }

  void _setupPaymentListeners() {
    _paymentService.paymentStatusStream.listen((status) {
      if (mounted) {
        setState(() {
          _paymentStatus = status;
        });
        
        if (status == PaymentStatus.success) {
          widget.onPaymentSuccess?.call();
          _showSuccessAndClose();
        }
      }
    });

    _paymentService.paymentProgressStream.listen((progress) {
      if (mounted) {
        setState(() {
          _paymentProgress = progress;
        });
      }
    });

    _paymentService.paymentMessageStream.listen((message) {
      if (mounted) {
        setState(() {
          _paymentMessage = message;
        });
      }
    });
  }

  void _showSuccessAndClose() {
    // Show success message briefly then close
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        Navigator.of(context).pop();
        widget.onClose?.call();
      }
    });
  }

  Future<void> _launchUrl(String url) async {
    try {
      if (!await launchUrl(Uri.parse(url))) {
        throw Exception('Could not launch URL: $url');
      }
    } catch (err) {
      debugPrint('Error launching URL: $err');
    }
  }

  Uint8List _convertBase64Image(String base64String) {
    return const Base64Decoder().convert(base64String.split(',').last);
  }

  Widget _buildPaymentStatusIndicator() {
    switch (_paymentStatus) {
      case PaymentStatus.checking:
        return Container(
          padding: const EdgeInsets.all(16),
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue.shade200),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _paymentMessage,
                      style: const TextStyle(
                        color: Colors.blue,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              LinearProgressIndicator(
                value: _paymentProgress / 100,
                backgroundColor: Colors.blue.shade100,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade600),
              ),
              const SizedBox(height: 8),
              Text(
                'Progress: ${_paymentProgress.toStringAsFixed(0)}%',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.blue.shade600,
                ),
              ),
            ],
          ),
        );

      case PaymentStatus.success:
        return Container(
          padding: const EdgeInsets.all(16),
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.green.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.green.shade200),
          ),
          child: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green.shade600),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  _paymentMessage,
                  style: TextStyle(
                    color: Colors.green.shade700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );

      case PaymentStatus.timeout:
        return Container(
          padding: const EdgeInsets.all(16),
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.orange.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.orange.shade200),
          ),
          child: Row(
            children: [
              Icon(Icons.access_time, color: Colors.orange.shade600),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  _paymentMessage,
                  style: TextStyle(
                    color: Colors.orange.shade700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );

      default:
        return const SizedBox.shrink();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog.fullscreen(
      child: Scaffold(
        appBar: AppBar(
          title: Row(
            children: [
              const Text('Choose your bank account'),
              if (_paymentStatus == PaymentStatus.checking) ...[
                const SizedBox(width: 16),
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                const SizedBox(width: 8),
                const Text(
                  'Auto-checking...',
                  style: TextStyle(fontSize: 14, color: Colors.blue),
                ),
              ],
            ],
          ),
          leading: IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              _paymentService.stopPaymentCheck();
              Navigator.of(context).pop();
              widget.onClose?.call();
            },
          ),
        ),
        body: SingleChildScrollView(
          child: Column(
            children: [
              // Payment Status Indicator
              _buildPaymentStatusIndicator(),

              // QR Code Section
              Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Image.memory(
                        _convertBase64Image(widget.qrCode),
                        width: 180,
                        height: 180,
                        gaplessPlayback: true,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Scan this QR code with your banking app or choose a bank below',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const Divider(),

              // Bank List
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: widget.bankUrls.length,
                itemBuilder: (context, index) {
                  final bank = widget.bankUrls[index];
                  return ListTile(
                    leading: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        bank['logo'] ?? '',
                        width: 50,
                        height: 50,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: 50,
                            height: 50,
                            color: Colors.grey.shade200,
                            child: const Icon(Icons.account_balance),
                          );
                        },
                      ),
                    ),
                    title: Text(
                      bank['name'] ?? 'Bank',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                    subtitle: Text(bank['description'] ?? ''),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () => _launchUrl(bank['link'] ?? ''),
                  );
                },
              ),

              // Action Buttons
              if (widget.invoiceId != null) ...[
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      if (_paymentStatus != PaymentStatus.success) ...[
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: _paymentStatus == PaymentStatus.checking
                                ? null
                                : () async {
                                    await _paymentService.manualPaymentCheck();
                                  },
                            icon: const Icon(Icons.refresh),
                            label: const Text('Check Payment Manually'),
                          ),
                        ),
                        const SizedBox(width: 12),
                      ],
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            _paymentService.stopPaymentCheck();
                            Navigator.of(context).pop();
                            widget.onClose?.call();
                          },
                          icon: const Icon(Icons.close),
                          label: Text(
                            _paymentStatus == PaymentStatus.checking
                                ? 'Close & Continue Checking'
                                : 'Close',
                          ),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: _paymentStatus == PaymentStatus.checking
                                ? Colors.orange
                                : null,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _paymentService.dispose();
    super.dispose();
  }
}
