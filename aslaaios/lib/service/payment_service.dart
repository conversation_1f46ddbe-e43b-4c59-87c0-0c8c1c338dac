import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constant.dart';

class PaymentService {
  static const int maxCheckAttempts = 60; // 5 minutes (60 * 5 seconds)
  static const Duration checkInterval = Duration(seconds: 5);

  Timer? _paymentCheckTimer;
  int _checkAttempts = 0;
  String? _currentInvoiceId;
  bool _isChecking = false;

  // Stream controllers for payment status updates
  final StreamController<PaymentStatus> _paymentStatusController =
      StreamController<PaymentStatus>.broadcast();
  final StreamController<double> _paymentProgressController =
      StreamController<double>.broadcast();
  final StreamController<String> _paymentMessageController =
      StreamController<String>.broadcast();

  // Getters for streams
  Stream<PaymentStatus> get paymentStatusStream =>
      _paymentStatusController.stream;
  Stream<double> get paymentProgressStream => _paymentProgressController.stream;
  Stream<String> get paymentMessageStream => _paymentMessageController.stream;

  /// Start automatic payment verification
  void startPaymentCheck(String invoiceId) {
    // Prevent multiple simultaneous checks
    if (_isChecking) {
      debugPrint(
          'Payment check already in progress for invoice: $_currentInvoiceId');
      return;
    }

    debugPrint('Starting payment verification for invoice: $invoiceId');

    _isChecking = true;
    _currentInvoiceId = invoiceId;
    _checkAttempts = 0;
    _paymentStatusController.add(PaymentStatus.checking);
    _paymentMessageController.add('Automatically checking payment status...');

    _paymentCheckTimer = Timer.periodic(checkInterval, (timer) async {
      _checkAttempts++;
      double progress = (_checkAttempts / maxCheckAttempts) * 100;
      _paymentProgressController
          .add(progress.clamp(0, 95)); // Don't show 100% until confirmed

      bool paymentConfirmed = await checkPaymentStatus(invoiceId);

      if (paymentConfirmed) {
        _paymentStatusController.add(PaymentStatus.success);
        _paymentProgressController.add(100);
        _paymentMessageController.add(
            'Payment confirmed! Your license has been extended successfully.');
        stopPaymentCheck();
        return;
      }

      if (_checkAttempts >= maxCheckAttempts) {
        _paymentStatusController.add(PaymentStatus.timeout);
        _paymentMessageController
            .add('Payment verification timed out. Please check manually.');
        stopPaymentCheck();
      }
    });
  }

  /// Stop payment verification
  void stopPaymentCheck() {
    _paymentCheckTimer?.cancel();
    _paymentCheckTimer = null;
    _checkAttempts = 0;
    _isChecking = false;
    debugPrint('Payment verification stopped');
  }

  /// Check payment status manually
  Future<bool> checkPaymentStatus(String invoiceId) async {
    try {
      debugPrint(
          'PaymentService:: Checking payment status for invoice: $invoiceId');

      final response = await http.get(
        Uri.parse('$API_HOST/api/hook/payment/check/$invoiceId'),
      );

      debugPrint(
          'PaymentService:: Payment check response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        debugPrint('PaymentService:: Payment check response: $data');

        if (data['success'] == true &&
            data['payment'] != null &&
            data['payment']['rows'] != null &&
            data['payment']['rows'].length > 0) {
          debugPrint(
              'PaymentService:: Payment confirmed! Attempting manual license creation...');
          // Payment confirmed, try to create license manually as backup
          await _createLicenseManually(invoiceId);
          return true;
        } else {
          debugPrint('PaymentService:: Payment not yet confirmed');
        }
      } else {
        debugPrint(
            'PaymentService:: Payment check failed with status: ${response.statusCode}');
      }
      return false;
    } catch (error) {
      debugPrint('PaymentService:: Payment check error: $error');
      return false;
    }
  }

  /// Create license using redirect-based iOS payment verification API
  Future<void> _createLicenseManually(String invoiceId) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? token = prefs.getString('token');

      if (token == null || token.isEmpty) {
        debugPrint(
            'PaymentService:: Missing or empty token for iOS license creation');
        debugPrint(
            'PaymentService:: Token: ${token?.isNotEmpty == true ? "present" : "missing"}');
        debugPrint(
            'PaymentService:: Skipping iOS license creation - user needs to complete authentication');
        return;
      }

      // Get the cost from stored payment info
      double? cost = await getStoredPaymentCost();
      if (cost == null) {
        debugPrint(
            'PaymentService:: No stored payment cost found, using default');
        cost = 15000; // Default to 3 months
      }

      debugPrint(
          'PaymentService:: Creating license via redirect API - Cost: $cost, Invoice: $invoiceId');

      // Use redirect-based payment verification endpoint
      final response = await http.post(
        Uri.parse('$API_HOST/api/ios/payment/verify-extend-redirect'),
        headers: {
          'Content-Type': 'application/json; charset=UTF-8',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({
          'invoiceId': invoiceId,
          'cost': cost,
          'platform': 'ios',
          'verificationMethod': 'automatic',
          'redirectUrl': '/main', // Where to redirect after success
        }),
      );

      debugPrint(
          'PaymentService:: Redirect API response status: ${response.statusCode}');
      debugPrint(
          'PaymentService:: Redirect API response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          debugPrint(
              'PaymentService:: License extended successfully via redirect API');
          debugPrint(
              'PaymentService:: New expiry date: ${data['newExpiryDate']}');
          debugPrint('PaymentService:: Days added: ${data['daysAdded']}');
          debugPrint('PaymentService:: Redirect URL: ${data['redirectUrl']}');

          // Trigger redirect through success callback
          _paymentStatusController.add(PaymentStatus.success);
          _paymentProgressController.add(100);
          _paymentMessageController
              .add('Payment confirmed! Redirecting to home page...');
        } else {
          debugPrint(
              'PaymentService:: Redirect API failed: ${data['message']}');
        }
      } else {
        debugPrint(
            'PaymentService:: Redirect API failed with status: ${response.statusCode}');
      }
    } catch (error) {
      debugPrint('PaymentService:: Redirect API error: $error');
    }
  }

  /// Store payment cost for later use
  Future<void> storePaymentCost(double cost) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setDouble('current_payment_cost', cost);
  }

  /// Get stored payment cost
  Future<double?> getStoredPaymentCost() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getDouble('current_payment_cost');
  }

  /// Manual payment check (for user-triggered checks)
  Future<bool> manualPaymentCheck() async {
    if (_currentInvoiceId == null) return false;

    _paymentMessageController.add('Checking payment status...');
    bool confirmed = await checkPaymentStatus(_currentInvoiceId!);

    if (!confirmed) {
      _paymentMessageController.add(
          'Payment not yet confirmed. Please complete your payment and try again.');
    }

    return confirmed;
  }

  /// Dispose resources
  void dispose() {
    stopPaymentCheck();
    _isChecking = false;
    _paymentStatusController.close();
    _paymentProgressController.close();
    _paymentMessageController.close();
    debugPrint('PaymentService disposed');
  }
}

/// Payment status enum
enum PaymentStatus {
  idle,
  checking,
  success,
  timeout,
  failed,
}

/// Extension for PaymentStatus to get display text
extension PaymentStatusExtension on PaymentStatus {
  String get displayText {
    switch (this) {
      case PaymentStatus.idle:
        return 'Ready';
      case PaymentStatus.checking:
        return 'Checking...';
      case PaymentStatus.success:
        return 'Success';
      case PaymentStatus.timeout:
        return 'Timeout';
      case PaymentStatus.failed:
        return 'Failed';
    }
  }
}
