import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constant.dart';

class PaymentService {
  static const int maxCheckAttempts = 60; // 5 minutes (60 * 5 seconds)
  static const Duration checkInterval = Duration(seconds: 5);

  Timer? _paymentCheckTimer;
  int _checkAttempts = 0;
  String? _currentInvoiceId;
  bool _isChecking = false;

  // Stream controllers for payment status updates
  final StreamController<PaymentStatus> _paymentStatusController =
      StreamController<PaymentStatus>.broadcast();
  final StreamController<double> _paymentProgressController =
      StreamController<double>.broadcast();
  final StreamController<String> _paymentMessageController =
      StreamController<String>.broadcast();

  // Getters for streams
  Stream<PaymentStatus> get paymentStatusStream =>
      _paymentStatusController.stream;
  Stream<double> get paymentProgressStream => _paymentProgressController.stream;
  Stream<String> get paymentMessageStream => _paymentMessageController.stream;

  /// Start automatic payment verification
  void startPaymentCheck(String invoiceId) {
    // Prevent multiple simultaneous checks
    if (_isChecking) {
      debugPrint(
          'Payment check already in progress for invoice: $_currentInvoiceId');
      return;
    }

    debugPrint('Starting payment verification for invoice: $invoiceId');

    _isChecking = true;
    _currentInvoiceId = invoiceId;
    _checkAttempts = 0;
    _paymentStatusController.add(PaymentStatus.checking);
    _paymentMessageController.add('Automatically checking payment status...');

    _paymentCheckTimer = Timer.periodic(checkInterval, (timer) async {
      _checkAttempts++;
      double progress = (_checkAttempts / maxCheckAttempts) * 100;
      _paymentProgressController
          .add(progress.clamp(0, 95)); // Don't show 100% until confirmed

      bool paymentConfirmed = await checkPaymentStatus(invoiceId);

      if (paymentConfirmed) {
        _paymentStatusController.add(PaymentStatus.success);
        _paymentProgressController.add(100);
        _paymentMessageController.add(
            'Payment confirmed! Your license has been extended successfully.');
        stopPaymentCheck();
        return;
      }

      if (_checkAttempts >= maxCheckAttempts) {
        _paymentStatusController.add(PaymentStatus.timeout);
        _paymentMessageController
            .add('Payment verification timed out. Please check manually.');
        stopPaymentCheck();
      }
    });
  }

  /// Stop payment verification
  void stopPaymentCheck() {
    _paymentCheckTimer?.cancel();
    _paymentCheckTimer = null;
    _checkAttempts = 0;
    _isChecking = false;
    debugPrint('Payment verification stopped');
  }

  /// Check payment status manually
  Future<bool> checkPaymentStatus(String invoiceId) async {
    try {
      final response = await http.get(
        Uri.parse('$API_HOST/api/hook/payment/check/$invoiceId'),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (data['success'] == true &&
            data['payment'] != null &&
            data['payment']['rows'] != null &&
            data['payment']['rows'].length > 0) {
          // Payment confirmed, try to create license manually as backup
          await _createLicenseManually(invoiceId);
          return true;
        }
      }
      return false;
    } catch (error) {
      debugPrint('Payment check error: $error');
      return false;
    }
  }

  /// Create license manually when payment is confirmed
  Future<void> _createLicenseManually(String invoiceId) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? token = prefs.getString('token');
      String? userId = prefs.getString('userId');

      if (token == null || userId == null) {
        debugPrint('Missing token or userId for manual license creation');
        return;
      }

      // Get the cost from stored payment info or calculate from invoice
      double cost = _getStoredPaymentCost() ?? 15000; // Default to 3 months

      final response = await http.post(
        Uri.parse('$API_HOST/api/hook/payment/create-license-manual'),
        headers: {
          'Content-Type': 'application/json; charset=UTF-8',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({
          'userId': userId,
          'cost': cost,
          'invoiceId': invoiceId,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          debugPrint('License created successfully via manual method');
        }
      }
    } catch (error) {
      debugPrint('Manual license creation failed: $error');
    }
  }

  /// Get stored payment cost (you'll need to store this when initiating payment)
  double? _getStoredPaymentCost() {
    // This should be stored when the payment is initiated
    // For now, return null to use default
    return null;
  }

  /// Store payment cost for later use
  Future<void> storePaymentCost(double cost) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setDouble('current_payment_cost', cost);
  }

  /// Get stored payment cost
  Future<double?> getStoredPaymentCost() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getDouble('current_payment_cost');
  }

  /// Manual payment check (for user-triggered checks)
  Future<bool> manualPaymentCheck() async {
    if (_currentInvoiceId == null) return false;

    _paymentMessageController.add('Checking payment status...');
    bool confirmed = await checkPaymentStatus(_currentInvoiceId!);

    if (!confirmed) {
      _paymentMessageController.add(
          'Payment not yet confirmed. Please complete your payment and try again.');
    }

    return confirmed;
  }

  /// Dispose resources
  void dispose() {
    stopPaymentCheck();
    _isChecking = false;
    _paymentStatusController.close();
    _paymentProgressController.close();
    _paymentMessageController.close();
    debugPrint('PaymentService disposed');
  }
}

/// Payment status enum
enum PaymentStatus {
  idle,
  checking,
  success,
  timeout,
  failed,
}

/// Extension for PaymentStatus to get display text
extension PaymentStatusExtension on PaymentStatus {
  String get displayText {
    switch (this) {
      case PaymentStatus.idle:
        return 'Ready';
      case PaymentStatus.checking:
        return 'Checking...';
      case PaymentStatus.success:
        return 'Success';
      case PaymentStatus.timeout:
        return 'Timeout';
      case PaymentStatus.failed:
        return 'Failed';
    }
  }
}
