{"words": {"register": "Register", "ok": "OK", "back": "Back", "proceed": "Proceed / Config", "save_change": "Save Changes", "delete_device": "Delete Device", "active": "Active", "inactive": "Inactive", "license": "License", "expired": "Expired", "period": "Period", "phone_number": "Phone Number", "device_number": "Device Number", "4g_device_number": "4g Device Number", "device_type": "Device Type", "uix_type": "UIX Type", "mobile_number": "Mobile Number", "create_code": "Create Pin Code", "change_code": "Change Pin Code", "nickname": "Your nick name", "old_pin": "Old Code", "new_pin": "New Code", "confirm_pin": "Confirm Code", "delete": "Delete", "edit": "Edit", "location": "Location", "detail": "Detail", "device": "Choose Your Device", "driver_index": "Driver Index", "device_4g": "4GD", "device_sms": "SMS", "get_status": "getting current status and Location of the car....", "default_device": "<PERSON>", "details": "Detail", "update": "Update", "subscribe": "Sub", "unsubscribe": "Unsub", "set_button": "Set Time", "add_btn": "Add", "ble_device": "BLE Device", "ble_index": "BLE Index", "gps_time_select": "Report Time", "license_has_expired": " Your account's license is expired", "get_new_key": "please get a new key", "expired_message": "Please extend license", "rentable_device": "Is Rentable", "device_name": "Device Name", "pay": "Pay with QPay", "start_rent": "Start Rent", "finish_rent": "Finish Rent", "driver_license": "Driver license", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "<PERSON><PERSON>", "save": "Save", "not_available": "Not Available"}, "home": {"unlock": "Unlock", "lock": "Lock", "off1": "Off1", "on1": "On1", "off2": "Off2", "on2": "On2", "stop": "Stop", "start": "Start", "device_type": "Device Type", "uix_type": "UIX type", "turn_on": "engine on sent success..", "turn_off": "engine off sent success..", "door_open": "unlock the door sent..", "door_close": "lock the door sent..", "informations": "Informations", "device_not_available": "device is not available", "speed": "speed", "unknown": "unknown", "device_name": "Name", "show_location": "Show Location", "hide_location": "Hide Location", "refresh": "Refresh", "engine": "Engine", "schedule": "Schedule", "history": "History", "mirror": "Mirror"}, "message": {}, "configure_drivers": {"numbers_of_drivers": "Numbers of Drivers"}, "menu": {"home": "Home", "user_management": "User management", "device_manage": "device management", "device_dashboard": "online devices", "profile": "Profile", "device": "License", "register": "Device Profile", "nickname": "Nickname & Pin code", "log": "Command Logs", "log_out": "Log out", "license": "License Logs", "mapLog": "Play on Map", "simLog": "Sim card Logs", "driver": "Configure Drivers", "gps": "GPS Logs", "time": "Control with time", "order": "Order", "rent": "Car Rent", "transactions": "Transactions", "withdraws": "Withdraws", "rentcars": "Rent Cars", "app_management": "App Management", "statistics": "Usage Statistics", "income_monitoring": "Income Monitoring", "help": "sms command list", "device_config": "Device Configuration", "installer_dashboard": "Installer Dashboard"}, "statistics": {"title": "Usage Statistics Dashboard", "subtitle": "Comprehensive analytics and insights for device commands and user activity", "tabs": {"overview": "Overview", "command_analytics": "Command Analytics", "device_performance": "<PERSON>ce Performance", "user_activity": "User Activity", "time_analysis": "Time Analysis"}, "filters": {"title": "Filters", "date_range": "Date Range", "start_date": "Start Date", "end_date": "End Date", "period": "Period", "device_number": "Device Number", "user_id": "User ID", "command_type": "Command Type", "apply_filters": "Apply Filters", "reset_filters": "Reset Filters", "export_data": "Export Data", "periods": {"hour": "Hour", "day": "Day", "week": "Week", "month": "Month"}, "command_types": {"all": "All Commands", "power_on": "Power On", "power_off": "Power Off", "lock": "Lock", "unlock": "Unlock", "location": "Location", "status": "Status", "other": "Other"}}, "charts": {"command_overview": {"title": "Command Overview", "total_commands": "Total Commands", "successful": "Successful", "failed": "Failed", "success_rate": "Success Rate"}, "response_time": {"title": "Response Time Analysis", "average_response": "Average Response Time", "fastest": "Fastest", "slowest": "Slowest"}, "device_analytics": {"title": "Device Analytics", "active_devices": "Active Devices", "commands_per_device": "Commands per Device", "device_performance": "<PERSON>ce Performance"}, "time_based": {"title": "Time-based Analysis", "commands_over_time": "Commands Over Time", "peak_hours": "Peak Hours", "activity_pattern": "Activity Pattern"}}, "tables": {"top_users": {"title": "Top Users", "user": "User", "total_commands": "Total Commands", "success_rate": "Success Rate", "avg_response_time": "Avg Response Time", "last_activity": "Last Activity", "view_details": "View Details"}, "detailed_logs": {"title": "Detailed Command Logs", "timestamp": "Timestamp", "user": "User", "device": "<PERSON><PERSON>", "command": "Command", "status": "Status", "response_time": "Response Time", "response": "Response"}}, "status": {"success": "Success", "failed": "Failed", "timeout": "Timeout", "pending": "Pending"}, "loading": "Loading statistics...", "no_data": "No data available for the selected period", "error": "Failed to load statistics data", "debug": {"title": "Debug Information", "user_role": "User Role", "loading": "Loading", "command_stats": "Command Stats", "top_users": "Top Users", "device_analytics": "Device Analytics", "time_based_stats": "Time Based Stats", "error": "Error", "create_sample_data": "Create Sample Data", "refresh_data": "Refresh Data", "loaded": "Loaded", "not_loaded": "Not loaded", "users": "users", "devices": "devices", "entries": "entries", "none": "None"}, "sample_data": {"creating": "Creating sample data...", "created": "Sample data created successfully!", "failed": "Failed to create sample data"}, "realtime": {"title": "Real-time Updates", "connected": "Connected", "connecting": "Connecting", "disconnected": "Disconnected", "error": "Error", "connection_error": "Connection Error", "refresh": "Refresh Statistics", "clear": "Clear Feed", "auto_refresh": "Auto Refresh", "show_details": "Show Details", "connection_details": "Connection Details", "client_id": "Client ID", "subscribers": "Subscribers", "callbacks": "Callbacks", "live_feed": "Live Device Feed", "waiting_for_data": "Waiting for real-time data...", "connect_to_see_data": "Connect to MQTT to see live data"}}, "installer": {"dashboard_title": "Installer Dashboard", "quick_actions": "Quick Actions", "add_user": "Add User", "reset_pin": "Reset PIN", "search_users": "Search Users", "search_placeholder": "Search by phone or device number", "search_button": "Search", "searching": "Searching...", "search_results": "Search Results", "device_label": "<PERSON><PERSON>", "no_device_assigned": "No device assigned", "last_payload": "Last Payload", "sim_info": "SIM Info", "expiration_date": "Expiration Date", "no_data": "No data", "expired": "Expired", "active": "Active", "version": "Version", "add_device": "Add <PERSON>", "delete_device": "Delete Device", "add_new_user": "Add New User", "phone_number": "Phone Number", "phone_placeholder": "Enter phone number", "cancel": "Cancel", "adding": "Adding...", "add_device_for": "Add Device for", "device_number": "Device Number", "device_placeholder": "Enter device number", "device_default_info": "Device will be created with default values: Type: 4g, UIX: CarV1.2, Default: Yes", "creating": "Creating...", "create_device": "Create Device", "reset_user_pin": "Reset User PIN", "reset_pin_info": "This will reset the user's PIN to \"0000\"", "resetting": "Resetting...", "delete_device_title": "Delete Device", "delete_device_confirm": "Are you sure you want to delete this device? This action cannot be undone.", "device_number_label": "Device Number", "deleting": "Deleting...", "notifications": {"search_term_required": "Please enter a search term", "no_users_found": "No users found matching your search", "search_failed": "Search failed. Please try again.", "search_connection_error": "Search failed. Please check your connection and try again.", "user_added": "User added successfully", "user_add_failed": "Failed to add user", "user_add_error": "Failed to add user. Please try again.", "device_created": "<PERSON>ce created successfully", "device_create_failed": "Failed to create device", "device_create_error": "Failed to create device. Please try again.", "pin_reset": "PIN reset to 0000 successfully", "pin_reset_failed": "Failed to reset PIN", "pin_reset_error": "Failed to reset PIN. Please try again.", "device_deleted": "<PERSON>ce deleted successfully", "device_delete_failed": "Failed to delete device", "device_delete_error": "Failed to delete device. Please try again."}}, "pinModal": {"title": "A strong pin code privacy your device", "mismatch_error": "Pin code mismatch "}, "device_profile": {"gps_information": "GPS Config", "device_information": "Device Information", "device_scan": "<PERSON><PERSON>", "license_information": "License Information", "request_license": "Request License", "increase_license": "Increase License", "total_price": "Total Price", "registered_bles": "BLE list"}, "device_log": {"device_number": "Device Number", "command": "Command", "sent": "Sent?", "response": "Response", "time_of_sent": "Time of Sent", "time_of_receive": "Time of Receive", "response_message": "Response Message", "http_or_hook": "Http or Hook"}, "invoice_log": {"invoice_number": "Invoice Number", "license_key": "License Key", "cost": "Cost", "created": "Created", "expired": "Expired", "real_invoice_id": "Real Invoice ID"}, "order": {"order_detail": "Order detail", "car_model": "Car Model", "date_time": "available time for installation", "spare_key": "Spare Key", "yes": "yes", "no": "no", "submit_order": "submit order", "order_price": "order deposit", "address": "address", "order_pricing": "Price: 200.000 with spare key, 230.000 without spare key"}, "driver": {"information": "Driver Information", "name": "Username", "address": "Address", "profile": "profile", "description": "ID of cityzen", "hint": "Та энэхүү цонхыг зөвхөн нэг л удаа бөглөх тул үнэн зөв бөглөн үү", "bank_name": "Bank Name", "bank_account": "Bank Account"}, "landing": {"hero-1-title": "Hero 1 title", "hero-1-label": "Hero 1 label", "hero-1-description": "Hero 1 description"}, "device_config": {"title": "Device Configuration", "device_number": "Device Number", "mqtt_broker": "Pool Broker", "mqtt_connected": "Connected", "mqtt_disconnected": "Disconnected", "mqtt_connection_failed": "Connection Failed", "firmware_version": "Firmware Version", "refresh_status": "Refresh Status", "update_firmware": "Update Firmware", "update_initiated": "Firmware update initiated", "update_failed": "Firmware update failed", "restart_device": "<PERSON><PERSON>", "restart_initiated": "Device restart initiated", "restart_failed": "Device restart failed", "update_sending": "Sending update command...", "update_waiting_response": "Waiting for device response...", "update_downloading": "Downloading firmware...", "update_installing": "Installing firmware...", "update_completed": "Update completed successfully!", "update_already_latest": "<PERSON><PERSON> is already on the latest version", "update_operation_failed": "Update failed - too many attempts, please try again later", "update_timeout": "Update timeout - device did not respond", "update_elapsed": "Elapsed time", "sound_enabled": "Sound Enabled", "sound_help": "Enable/disable device beep sounds", "key_enabled": "Key Detection", "key_help": "Enable/disable key detection functionality", "server": "Server", "server_help": "Select which server to connect to", "protocol": "Protocol", "protocol_help": "Switch between XMPP and LwM2M protocols", "auto_shutdown": "Auto Shutdown Timer (minutes)", "auto_shutdown_help": "Time before device automatically powers off (0-120 minutes)", "auto_shutdown_feature": "Auto Shutdown Feature", "auto_shutdown_feature_help": "Enable/disable auto-shutdown functionality", "log_level": "Log Level", "log_level_help": "Set the verbosity of device logging", "geely_atlas_mode": "<PERSON><PERSON>", "geely_atlas_help": "Special key control mode for Geely Atlas vehicles", "voltage_offset": "Voltage Offset", "voltage_offset_help": "Adjust the reported battery voltage (0-2V)", "notifications_enabled": "Notifications Enabled", "notifications_help": "Enable/disable voltage change notifications", "voltage_threshold": "Voltage Threshold", "voltage_threshold_help": "Set the threshold for voltage change notifications (0-2V)", "gps_enabled": "GPS Enabled", "gps_help": "Enable/disable GPS tracking functionality", "save_success": "Configuration saved successfully", "save_error": "Failed to save configuration"}, "income": {"title": "Income Monitoring Dashboard", "subtitle": "Track and analyze revenue from installation orders and license subscriptions", "no_data": "No data available", "transactions": "transactions", "tabs": {"overview": "Overview", "trends": "Trends", "breakdown": "Breakdown", "growth": "Growth Analysis"}, "filters": {"title": "Income Filters", "start_date": "Start Date", "end_date": "End Date", "period": "Period", "daily": "Daily", "monthly": "Monthly", "yearly": "Yearly", "apply": "Apply", "reset": "Reset", "refresh": "Refresh Data", "expand": "Expand Filters", "collapse": "Collapse Filters", "quick_ranges": "Quick Date Ranges", "today": "Today", "this_week": "This Week", "this_month": "This Month", "last_3_months": "Last 3 Months", "last_6_months": "Last 6 Months", "this_year": "This Year", "last_year": "Last Year", "current_selection": "Current Selection", "date_range": "Date Range", "grouping": "Grouping"}, "metrics": {"total_income": "Total Income", "total_income_desc": "Combined revenue from all sources", "total_transactions": "Total Transactions", "total_transactions_desc": "Orders and subscriptions combined", "order_income": "Installation Orders", "order_income_desc": "Revenue from device installations", "license_income": "License Subscriptions", "license_income_desc": "Revenue from monthly subscriptions", "avg_daily": "Average Daily Income", "avg_daily_desc": "Daily average based on total income", "over_period": "Over selected period", "income_distribution": "Income Distribution", "orders": "Installation Orders", "licenses": "License Subscriptions", "distribution_desc": "Percentage breakdown of revenue sources"}, "sources": {"orders": "Installation Orders", "licenses": "License Subscriptions", "orders_short": "Orders", "licenses_short": "Licenses"}, "charts": {"overview": "Income Overview", "breakdown": "Income Breakdown", "trends": "Income Trends", "growth_analysis": "Growth Analysis", "revenue_distribution": "Revenue Distribution", "transaction_counts": "Transaction Counts", "total_revenue": "Total Revenue", "key_insights": "Key Insights", "avg_order_value": "Average Order Value", "avg_license_value": "Average License Value", "total_transactions": "Total Transactions", "avg_daily_income": "Avg Daily Income", "income_over_time": "Income Over Time", "transaction_trends": "Transaction Trends", "period_summary": "Period Summary", "total_income": "Total Income", "average_income": "Average Income", "highest_period": "Highest Period", "lowest_period": "Lowest Period", "periods": "periods", "transactions": "transactions", "transaction_count": "Transaction Count", "orders": "Orders", "licenses": "Licenses"}, "breakdown": {"source": "Source", "amount": "Amount", "percentage": "%", "total_income": "Total Income", "total_transactions": "Total Transactions", "avg_transaction": "Avg Transaction"}, "growth": {"current_period": "Current Period", "previous_period": "Previous Period", "period_comparison": "Period Comparison", "income_growth": "Income Growth", "transaction_growth": "Transaction Growth", "increase": "Increase", "decrease": "Decrease", "no_change": "No Change", "income": "Income", "transactions": "Transactions", "insights": "Growth Insights", "positive_insight": "Income has grown compared to the previous period.", "negative_insight": "Income has decreased compared to the previous period.", "neutral_insight": "Income has remained stable compared to the previous period."}, "export": {"button": "Export Data", "excel": "Export to Excel", "csv": "Export to CSV", "json": "Export to JSON", "pdf": "Export to PDF", "excel_success": "Excel export completed successfully", "csv_success": "CSV export completed successfully", "json_success": "JSON export completed successfully", "excel_error": "Failed to export Excel data", "csv_error": "Failed to export CSV data", "json_error": "Failed to export JSON data", "pdf_placeholder": "PDF export functionality will be implemented in a future update"}, "revenue_sources": "Revenue Sources"}, "nav": {"home": "Home", "admin": "Admin", "user_management_desc": "Manage users and permissions", "orders_desc": "View and manage installation orders", "app_management_desc": "Manage app versions and configurations", "statistics_desc": "View system usage analytics", "installer_desc": "Installer management tools"}}