(this.webpackJsonpclient=this.webpackJsonpclient||[]).push([[5],{606:function(t,e,n){"use strict";n.d(e,"a",(function(){return Nt}));var o=n(8),i=n(0);const r=/^[a-z0-9]+(-[a-z0-9]+)*$/,c=Object.freeze({left:0,top:0,width:16,height:16,rotate:0,vFlip:!1,hFlip:!1});function s(t){return Object(o.a)(Object(o.a)({},c),t)}const a=function(t,e,n){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"";const i=t.split(":");if("@"===t.slice(0,1)){if(i.length<2||i.length>3)return null;o=i.shift().slice(1)}if(i.length>3||!i.length)return null;if(i.length>1){const t=i.pop(),n=i.pop(),r={provider:i.length>0?i[0]:o,prefix:n,name:t};return e&&!l(r)?null:r}const r=i[0],c=r.split("-");if(c.length>1){const t={provider:o,prefix:c.shift(),name:c.join("-")};return e&&!l(t)?null:t}if(n&&""===o){const t={provider:o,prefix:"",name:r};return e&&!l(t,n)?null:t}return null},l=(t,e)=>!!t&&!(""!==t.provider&&!t.provider.match(r)||!(e&&""===t.prefix||t.prefix.match(r))||!t.name.match(r));function f(t,e){const n=Object(o.a)({},t);for(const o in c){const t=o;if(void 0!==e[t]){const o=e[t];if(void 0===n[t]){n[t]=o;continue}switch(t){case"rotate":n[t]=(n[t]+o)%4;break;case"hFlip":case"vFlip":n[t]=o!==n[t];break;default:n[t]=o}}}return n}function u(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];function o(e,n){if(void 0!==t.icons[e])return Object.assign({},t.icons[e]);if(n>5)return null;const i=t.aliases;if(i&&void 0!==i[e]){const t=i[e],r=o(t.parent,n+1);return r?f(r,t):r}const r=t.chars;return!n&&r&&void 0!==r[e]?o(r[e],n+1):null}const i=o(e,0);if(i)for(const r in c)void 0===i[r]&&void 0!==t[r]&&(i[r]=t[r]);return i&&n?s(i):i}function d(t,e,n){n=n||{};const o=[];if("object"!==typeof t||"object"!==typeof t.icons)return o;t.not_found instanceof Array&&t.not_found.forEach((t=>{e(t,null),o.push(t)}));const i=t.icons;Object.keys(i).forEach((n=>{const i=u(t,n,!0);i&&(e(n,i),o.push(n))}));const r=n.aliases||"all";if("none"!==r&&"object"===typeof t.aliases){const n=t.aliases;Object.keys(n).forEach((i=>{if("variations"===r&&function(t){for(const e in c)if(void 0!==t[e])return!0;return!1}(n[i]))return;const s=u(t,i,!0);s&&(e(i,s),o.push(i))}))}return o}const p={provider:"string",aliases:"object",not_found:"object"};for(const zt in c)p[zt]=typeof c[zt];function h(t){if("object"!==typeof t||null===t)return null;const e=t;if("string"!==typeof e.prefix||!t.icons||"object"!==typeof t.icons)return null;for(const i in p)if(void 0!==t[i]&&typeof t[i]!==p[i])return null;const n=e.icons;for(const i in n){const t=n[i];if(!i.match(r)||"string"!==typeof t.body)return null;for(const e in c)if(void 0!==t[e]&&typeof t[e]!==typeof c[e])return null}const o=e.aliases;if(o)for(const i in o){const t=o[i],e=t.parent;if(!i.match(r)||"string"!==typeof e||!n[e]&&!o[e])return null;for(const n in c)if(void 0!==t[n]&&typeof t[n]!==typeof c[n])return null}return e}let g=Object.create(null);try{const t=window||self;t&&1===t._iconifyStorage.version&&(g=t._iconifyStorage.storage)}catch(Ut){}function b(t,e){void 0===g[t]&&(g[t]=Object.create(null));const n=g[t];return void 0===n[e]&&(n[e]=function(t,e){return{provider:t,prefix:e,icons:Object.create(null),missing:Object.create(null)}}(t,e)),n[e]}function v(t,e){if(!h(e))return[];const n=Date.now();return d(e,((e,o)=>{o?t.icons[e]=o:t.missing[e]=n}))}function m(t,e){const n=t.icons[e];return void 0===n?null:n}let y=!1;function j(t){return"boolean"===typeof t&&(y=t),y}function x(t){const e="string"===typeof t?a(t,!0,y):t;return e?m(b(e.provider,e.prefix),e.name):null}function w(t,e){const n=a(t,!0,y);if(!n)return!1;return function(t,e,n){try{if("string"===typeof n.body)return t.icons[e]=Object.freeze(s(n)),!0}catch(Ut){}return!1}(b(n.provider,n.prefix),n.name,e)}const O=Object.freeze({inline:!1,width:null,height:null,hAlign:"center",vAlign:"middle",slice:!1,hFlip:!1,vFlip:!1,rotate:0});function k(t,e){const n={};for(const o in t){const i=o;if(n[i]=t[i],void 0===e[i])continue;const r=e[i];switch(i){case"inline":case"slice":"boolean"===typeof r&&(n[i]=r);break;case"hFlip":case"vFlip":!0===r&&(n[i]=!n[i]);break;case"hAlign":case"vAlign":"string"===typeof r&&""!==r&&(n[i]=r);break;case"width":case"height":("string"===typeof r&&""!==r||"number"===typeof r&&r||null===r)&&(n[i]=r);break;case"rotate":"number"===typeof r&&(n[i]+=r)}}return n}const _=/(-?[0-9.]*[0-9]+[0-9.]*)/g,S=/^-?[0-9.]*[0-9]+[0-9.]*$/g;function E(t,e,n){if(1===e)return t;if(n=void 0===n?100:n,"number"===typeof t)return Math.ceil(t*e*n)/n;if("string"!==typeof t)return t;const o=t.split(_);if(null===o||!o.length)return t;const i=[];let r=o.shift(),c=S.test(r);for(;;){if(c){const t=parseFloat(r);isNaN(t)?i.push(r):i.push(Math.ceil(t*e*n)/n)}else i.push(r);if(r=o.shift(),void 0===r)return i.join("");c=!c}}function I(t){let e="";switch(t.hAlign){case"left":e+="xMin";break;case"right":e+="xMax";break;default:e+="xMid"}switch(t.vAlign){case"top":e+="YMin";break;case"bottom":e+="YMax";break;default:e+="YMid"}return e+=t.slice?" slice":" meet",e}function M(t,e){const n={left:t.left,top:t.top,width:t.width,height:t.height};let o,i,r=t.body;[t,e].forEach((t=>{const e=[],o=t.hFlip,i=t.vFlip;let c,s=t.rotate;switch(o?i?s+=2:(e.push("translate("+(n.width+n.left).toString()+" "+(0-n.top).toString()+")"),e.push("scale(-1 1)"),n.top=n.left=0):i&&(e.push("translate("+(0-n.left).toString()+" "+(n.height+n.top).toString()+")"),e.push("scale(1 -1)"),n.top=n.left=0),s<0&&(s-=4*Math.floor(s/4)),s%=4,s){case 1:c=n.height/2+n.top,e.unshift("rotate(90 "+c.toString()+" "+c.toString()+")");break;case 2:e.unshift("rotate(180 "+(n.width/2+n.left).toString()+" "+(n.height/2+n.top).toString()+")");break;case 3:c=n.width/2+n.left,e.unshift("rotate(-90 "+c.toString()+" "+c.toString()+")")}s%2===1&&(0===n.left&&0===n.top||(c=n.left,n.left=n.top,n.top=c),n.width!==n.height&&(c=n.width,n.width=n.height,n.height=c)),e.length&&(r='<g transform="'+e.join(" ")+'">'+r+"</g>")})),null===e.width&&null===e.height?(i="1em",o=E(i,n.width/n.height)):null!==e.width&&null!==e.height?(o=e.width,i=e.height):null!==e.height?(i=e.height,o=E(i,n.width/n.height)):(o=e.width,i=E(o,n.height/n.width)),"auto"===o&&(o=n.width),"auto"===i&&(i=n.height),o="string"===typeof o?o:o.toString()+"",i="string"===typeof i?i:i.toString()+"";const c={attributes:{width:o,height:i,preserveAspectRatio:I(e),viewBox:n.left.toString()+" "+n.top.toString()+" "+n.width.toString()+" "+n.height.toString()},body:r};return e.inline&&(c.inline=!0),c}const A=/\sid="(\S+)"/g,D="IconifyId"+Date.now().toString(16)+(16777216*Math.random()|0).toString(16);let T=0;function R(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:D;const n=[];let o;for(;o=A.exec(t);)n.push(o[1]);return n.length?(n.forEach((n=>{const o="function"===typeof e?e(n):e+(T++).toString(),i=n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");t=t.replace(new RegExp('([#;"])('+i+')([")]|\\.[a-z])',"g"),"$1"+o+"$3")})),t):t}const F=Object.create(null);function L(t,e){F[t]=e}function N(t){return F[t]||F[""]}function U(t){let e;if("string"===typeof t.resources)e=[t.resources];else if(e=t.resources,!(e instanceof Array)||!e.length)return null;return{resources:e,path:void 0===t.path?"/":t.path,maxURL:t.maxURL?t.maxURL:500,rotate:t.rotate?t.rotate:750,timeout:t.timeout?t.timeout:5e3,random:!0===t.random,index:t.index?t.index:0,dataAfterTimeout:!1!==t.dataAfterTimeout}}const P=Object.create(null),z=["https://api.simplesvg.com","https://api.unisvg.com"],C=[];for(;z.length>0;)1===z.length||Math.random()>.5?C.push(z.shift()):C.push(z.pop());function $(t,e){const n=U(e);return null!==n&&(P[t]=n,!0)}function q(t){return P[t]}P[""]=U({resources:["https://api.iconify.design"].concat(C)});const J=(t,e)=>{let n=t,o=-1!==n.indexOf("?");return Object.keys(e).forEach((t=>{let i;try{i=function(t){switch(typeof t){case"boolean":return t?"true":"false";case"number":case"string":return encodeURIComponent(t);default:throw new Error("Invalid parameter")}}(e[t])}catch(Ut){return}n+=(o?"&":"?")+encodeURIComponent(t)+"="+i,o=!0})),n},B={},Y={};let H=(()=>{let t;try{if(t=fetch,"function"===typeof t)return t}catch(Ut){}return null})();const W={prepare:(t,e,n)=>{const o=[];let i=B[e];void 0===i&&(i=function(t,e){const n=q(t);if(!n)return 0;let o;if(n.maxURL){let t=0;n.resources.forEach((e=>{const n=e;t=Math.max(t,n.length)}));const i=J(e+".json",{icons:""});o=n.maxURL-t-n.path.length-i.length}else o=0;const i=t+":"+e;return Y[t]=n.path,B[i]=o,o}(t,e));const r="icons";let c={type:r,provider:t,prefix:e,icons:[]},s=0;return n.forEach(((n,a)=>{s+=n.length+1,s>=i&&a>0&&(o.push(c),c={type:r,provider:t,prefix:e,icons:[]},s=n.length),c.icons.push(n)})),o.push(c),o},send:(t,e,n)=>{if(!H)return void n("abort",424);let o=function(t){if("string"===typeof t){if(void 0===Y[t]){const e=q(t);if(!e)return"/";Y[t]=e.path}return Y[t]}return"/"}(e.provider);switch(e.type){case"icons":{const t=e.prefix,n=e.icons.join(",");o+=J(t+".json",{icons:n});break}case"custom":{const t=e.uri;o+="/"===t.slice(0,1)?t.slice(1):t;break}default:return void n("abort",400)}let i=503;H(t+o).then((t=>{const e=t.status;if(200===e)return i=501,t.json();setTimeout((()=>{n(function(t){return 404===t}(e)?"abort":"next",e)}))})).then((t=>{"object"===typeof t&&null!==t?setTimeout((()=>{n("success",t)})):setTimeout((()=>{n("next",i)}))})).catch((()=>{n("next",i)}))}};const X=Object.create(null),G=Object.create(null);function K(t,e){t.forEach((t=>{const n=t.provider;if(void 0===X[n])return;const o=X[n],i=t.prefix,r=o[i];r&&(o[i]=r.filter((t=>t.id!==e)))}))}let Q=0;var V={resources:[],index:0,timeout:2e3,rotate:750,random:!1,dataAfterTimeout:!1};function Z(t,e,n,o){const i=t.resources.length,r=t.random?Math.floor(Math.random()*i):t.index;let c;if(t.random){let e=t.resources.slice(0);for(c=[];e.length>1;){const t=Math.floor(Math.random()*e.length);c.push(e[t]),e=e.slice(0,t).concat(e.slice(t+1))}c=c.concat(e)}else c=t.resources.slice(r).concat(t.resources.slice(0,r));const s=Date.now();let a,l="pending",f=0,u=null,d=[],p=[];function h(){u&&(clearTimeout(u),u=null)}function g(){"pending"===l&&(l="aborted"),h(),d.forEach((t=>{"pending"===t.status&&(t.status="aborted")})),d=[]}function b(t,e){e&&(p=[]),"function"===typeof t&&p.push(t)}function v(){l="failed",p.forEach((t=>{t(void 0,a)}))}function m(){d.forEach((t=>{"pending"===t.status&&(t.status="aborted")})),d=[]}function y(){if("pending"!==l)return;h();const o=c.shift();if(void 0===o)return d.length?void(u=setTimeout((()=>{h(),"pending"===l&&(m(),v())}),t.timeout)):void v();const i={status:"pending",resource:o,callback:(e,n)=>{!function(e,n,o){const i="success"!==n;switch(d=d.filter((t=>t!==e)),l){case"pending":break;case"failed":if(i||!t.dataAfterTimeout)return;break;default:return}if("abort"===n)return a=o,void v();if(i)return a=o,void(d.length||(c.length?y():v()));if(h(),m(),!t.random){const n=t.resources.indexOf(e.resource);-1!==n&&n!==t.index&&(t.index=n)}l="completed",p.forEach((t=>{t(o)}))}(i,e,n)}};d.push(i),f++,u=setTimeout(y,t.rotate),n(o,e,i.callback)}return"function"===typeof o&&p.push(o),setTimeout(y),function(){return{startTime:s,payload:e,status:l,queriesSent:f,queriesPending:d.length,subscribe:b,abort:g}}}function tt(t){const e=function(t){if("object"!==typeof t||"object"!==typeof t.resources||!(t.resources instanceof Array)||!t.resources.length)throw new Error("Invalid Reduncancy configuration");const e=Object.create(null);let n;for(n in V)void 0!==t[n]?e[n]=t[n]:e[n]=V[n];return e}(t);let n=[];function o(){n=n.filter((t=>"pending"===t().status))}return{query:function(t,i,r){const c=Z(e,t,i,((t,e)=>{o(),r&&r(t,e)}));return n.push(c),c},find:function(t){const e=n.find((e=>t(e)));return void 0!==e?e:null},setIndex:t=>{e.index=t},getIndex:()=>e.index,cleanup:o}}function et(){}const nt=Object.create(null);function ot(t,e,n){let o,i;if("string"===typeof t){const e=N(t);if(!e)return n(void 0,424),et;i=e.send;const r=function(t){if(void 0===nt[t]){const e=q(t);if(!e)return;const n={config:e,redundancy:tt(e)};nt[t]=n}return nt[t]}(t);r&&(o=r.redundancy)}else{const e=U(t);if(e){o=tt(e);const n=N(t.resources?t.resources[0]:"");n&&(i=n.send)}}return o&&i?o.query(e,i,n)().abort:(n(void 0,424),et)}const it={};function rt(){}const ct=Object.create(null),st=Object.create(null),at=Object.create(null),lt=Object.create(null);function ft(t,e){void 0===at[t]&&(at[t]=Object.create(null));const n=at[t];n[e]||(n[e]=!0,setTimeout((()=>{n[e]=!1,function(t,e){void 0===G[t]&&(G[t]=Object.create(null));const n=G[t];n[e]||(n[e]=!0,setTimeout((()=>{if(n[e]=!1,void 0===X[t]||void 0===X[t][e])return;const o=X[t][e].slice(0);if(!o.length)return;const i=b(t,e);let r=!1;o.forEach((n=>{const o=n.icons,c=o.pending.length;o.pending=o.pending.filter((n=>{if(n.prefix!==e)return!0;const c=n.name;if(void 0!==i.icons[c])o.loaded.push({provider:t,prefix:e,name:c});else{if(void 0===i.missing[c])return r=!0,!0;o.missing.push({provider:t,prefix:e,name:c})}return!1})),o.pending.length!==c&&(r||K([{provider:t,prefix:e}],n.id),n.callback(o.loaded.slice(0),o.missing.slice(0),o.pending.slice(0),n.abort))}))})))}(t,e)})))}const ut=Object.create(null);function dt(t,e,n){void 0===st[t]&&(st[t]=Object.create(null));const o=st[t];void 0===lt[t]&&(lt[t]=Object.create(null));const i=lt[t];void 0===ct[t]&&(ct[t]=Object.create(null));const r=ct[t];void 0===o[e]?o[e]=n:o[e]=o[e].concat(n).sort(),i[e]||(i[e]=!0,setTimeout((()=>{i[e]=!1;const n=o[e];delete o[e];const c=N(t);if(!c)return void function(){const n=(""===t?"":"@"+t+":")+e,o=Math.floor(Date.now()/6e4);ut[n]<o&&(ut[n]=o,console.error('Unable to retrieve icons for "'+n+'" because API is not configured properly.'))}();c.prepare(t,e,n).forEach((n=>{ot(t,n,((o,i)=>{const c=b(t,e);if("object"!==typeof o){if(404!==i)return;const t=Date.now();n.icons.forEach((e=>{c.missing[e]=t}))}else try{const n=v(c,o);if(!n.length)return;const i=r[e];n.forEach((t=>{delete i[t]})),it.store&&it.store(t,o)}catch(s){console.error(s)}ft(t,e)}))}))})))}const pt=(t,e)=>{const n=function(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const o=[];return t.forEach((t=>{const i="string"===typeof t?a(t,!1,n):t;e&&!l(i,n)||o.push({provider:i.provider,prefix:i.prefix,name:i.name})})),o}(t,!0,j()),o=function(t){const e={loaded:[],missing:[],pending:[]},n=Object.create(null);t.sort(((t,e)=>t.provider!==e.provider?t.provider.localeCompare(e.provider):t.prefix!==e.prefix?t.prefix.localeCompare(e.prefix):t.name.localeCompare(e.name)));let o={provider:"",prefix:"",name:""};return t.forEach((t=>{if(o.name===t.name&&o.prefix===t.prefix&&o.provider===t.provider)return;o=t;const i=t.provider,r=t.prefix,c=t.name;void 0===n[i]&&(n[i]=Object.create(null));const s=n[i];void 0===s[r]&&(s[r]=b(i,r));const a=s[r];let l;l=void 0!==a.icons[c]?e.loaded:""===r||void 0!==a.missing[c]?e.missing:e.pending;const f={provider:i,prefix:r,name:c};l.push(f)})),e}(n);if(!o.pending.length){let t=!0;return e&&setTimeout((()=>{t&&e(o.loaded,o.missing,o.pending,rt)})),()=>{t=!1}}const i=Object.create(null),r=[];let c,s;o.pending.forEach((t=>{const e=t.provider,n=t.prefix;if(n===s&&e===c)return;c=e,s=n,r.push({provider:e,prefix:n}),void 0===ct[e]&&(ct[e]=Object.create(null));const o=ct[e];void 0===o[n]&&(o[n]=Object.create(null)),void 0===i[e]&&(i[e]=Object.create(null));const a=i[e];void 0===a[n]&&(a[n]=[])}));const f=Date.now();return o.pending.forEach((t=>{const e=t.provider,n=t.prefix,o=t.name,r=ct[e][n];void 0===r[o]&&(r[o]=f,i[e][n].push(o))})),r.forEach((t=>{const e=t.provider,n=t.prefix;i[e][n].length&&dt(e,n,i[e][n])})),e?function(t,e,n){const o=Q++,i=K.bind(null,n,o);if(!e.pending.length)return i;const r={id:o,icons:e,callback:t,abort:i};return n.forEach((t=>{const e=t.provider,n=t.prefix;void 0===X[e]&&(X[e]=Object.create(null));const o=X[e];void 0===o[n]&&(o[n]=[]),o[n].push(r)})),i}(e,o,r):rt},ht="iconify2",gt="iconify",bt=gt+"-count",vt=gt+"-version",mt=36e5,yt={local:!0,session:!0};let jt=!1;const xt={local:0,session:0},wt={local:[],session:[]};let Ot="undefined"===typeof window?{}:window;function kt(t){const e=t+"Storage";try{if(Ot&&Ot[e]&&"number"===typeof Ot[e].length)return Ot[e]}catch(Ut){}return yt[t]=!1,null}function _t(t,e,n){try{return t.setItem(bt,n.toString()),xt[e]=n,!0}catch(Ut){return!1}}function St(t){const e=t.getItem(bt);if(e){const t=parseInt(e);return t||0}return 0}const Et=()=>{if(jt)return;jt=!0;const t=Math.floor(Date.now()/mt)-168;function e(e){const n=kt(e);if(!n)return;const o=e=>{const o=gt+e.toString(),i=n.getItem(o);if("string"!==typeof i)return!1;let r=!0;try{const e=JSON.parse(i);if("object"!==typeof e||"number"!==typeof e.cached||e.cached<t||"string"!==typeof e.provider||"object"!==typeof e.data||"string"!==typeof e.data.prefix)r=!1;else{const t=e.provider,n=e.data.prefix;r=v(b(t,n),e.data).length>0}}catch(Ut){r=!1}return r||n.removeItem(o),r};try{const t=n.getItem(vt);if(t!==ht)return t&&function(t){try{const e=St(t);for(let n=0;n<e;n++)t.removeItem(gt+n.toString())}catch(Ut){}}(n),void function(t,e){try{t.setItem(vt,ht)}catch(Ut){}_t(t,e,0)}(n,e);let i=St(n);for(let n=i-1;n>=0;n--)o(n)||(n===i-1?i--:wt[e].push(n));_t(n,e,i)}catch(Ut){}}for(const n in yt)e(n)},It=(t,e)=>{function n(n){if(!yt[n])return!1;const o=kt(n);if(!o)return!1;let i=wt[n].shift();if(void 0===i&&(i=xt[n],!_t(o,n,i+1)))return!1;try{const n={cached:Math.floor(Date.now()/mt),provider:t,data:e};o.setItem(gt+i.toString(),JSON.stringify(n))}catch(Ut){return!1}return!0}jt||Et(),Object.keys(e.icons).length&&(e.not_found&&delete(e=Object.assign({},e)).not_found,n("local")||n("session"))};const Mt=/[\s,]+/;function At(t,e){e.split(Mt).forEach((e=>{switch(e.trim()){case"horizontal":t.hFlip=!0;break;case"vertical":t.vFlip=!0}}))}function Dt(t,e){e.split(Mt).forEach((e=>{const n=e.trim();switch(n){case"left":case"center":case"right":t.hAlign=n;break;case"top":case"middle":case"bottom":t.vAlign=n;break;case"slice":case"crop":t.slice=!0;break;case"meet":t.slice=!1}}))}function Tt(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;const n=t.replace(/^-?[0-9.]*/,"");function o(t){for(;t<0;)t+=4;return t%4}if(""===n){const e=parseInt(t);return isNaN(e)?0:o(e)}if(n!==t){let e=0;switch(n){case"%":e=25;break;case"deg":e=90}if(e){let i=parseFloat(t.slice(0,t.length-n.length));return isNaN(i)?0:(i/=e,i%1===0?o(i):0)}}return e}const Rt={xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink","aria-hidden":!0,role:"img",style:{}},Ft=Object(o.a)(Object(o.a)({},O),{},{inline:!0});if(j(!0),L("",W),"undefined"!==typeof document&&"undefined"!==typeof window){it.store=It,Et();const t=window;if(void 0!==t.IconifyPreload){const e=t.IconifyPreload,n="Invalid IconifyPreload syntax.";"object"===typeof e&&null!==e&&(e instanceof Array?e:[e]).forEach((t=>{try{("object"!==typeof t||null===t||t instanceof Array||"object"!==typeof t.icons||"string"!==typeof t.prefix||!function(t,e){if("object"!==typeof t)return!1;if("string"!==typeof e&&(e="string"===typeof t.provider?t.provider:""),y&&""===e&&("string"!==typeof t.prefix||""===t.prefix)){let e=!1;return h(t)&&(t.prefix="",d(t,((t,n)=>{n&&w(t,n)&&(e=!0)}))),e}return!("string"!==typeof t.prefix||!l({provider:e,prefix:t.prefix,name:"a"}))&&!!v(b(e,t.prefix),t)}(t))&&console.error(n)}catch(e){console.error(n)}}))}if(void 0!==t.IconifyProviders){const e=t.IconifyProviders;if("object"===typeof e&&null!==e)for(let t in e){const n="IconifyProviders["+t+"] is invalid.";try{const o=e[t];if("object"!==typeof o||!o||void 0===o.resources)continue;$(t,o)||console.error(n)}catch(Pt){console.error(n)}}}}class Lt extends i.Component{constructor(t){super(t),this.state={icon:null}}_abortLoading(){this._loading&&(this._loading.abort(),this._loading=null)}_setData(t){this.state.icon!==t&&this.setState({icon:t})}_checkIcon(t){const e=this.state,n=this.props.icon;if("object"===typeof n&&null!==n&&"string"===typeof n.body)return this._icon="",this._abortLoading(),void((t||null===e.icon)&&this._setData({data:s(n)}));let o;if("string"!==typeof n||null===(o=a(n,!1,!0)))return this._abortLoading(),void this._setData(null);const i=x(o);if(null!==i){if(this._icon!==n||null===e.icon){this._abortLoading(),this._icon=n;const t=["iconify"];""!==o.prefix&&t.push("iconify--"+o.prefix),""!==o.provider&&t.push("iconify--"+o.provider),this._setData({data:i,classes:t}),this.props.onLoad&&this.props.onLoad(n)}}else this._loading&&this._loading.name===n||(this._abortLoading(),this._icon="",this._setData(null),this._loading={name:n,abort:pt([o],this._checkIcon.bind(this,!1))})}componentDidMount(){this._checkIcon(!1)}componentDidUpdate(t){t.icon!==this.props.icon&&this._checkIcon(!0)}componentWillUnmount(){this._abortLoading()}render(){const t=this.props,e=this.state.icon;if(null===e)return t.children?t.children:i.createElement("span",{});let n=t;return e.classes&&(n=Object(o.a)(Object(o.a)({},t),{},{className:("string"===typeof t.className?t.className+" ":"")+e.classes.join(" ")})),((t,e,n,r)=>{const c=n?Ft:O,s=k(c,e),a="object"===typeof e.style&&null!==e.style?e.style:{},l=Object(o.a)(Object(o.a)({},Rt),{},{ref:r,style:a});for(let o in e){const t=e[o];if(void 0!==t)switch(o){case"icon":case"style":case"children":case"onLoad":case"_ref":case"_inline":break;case"inline":case"hFlip":case"vFlip":s[o]=!0===t||"true"===t||1===t;break;case"flip":"string"===typeof t&&At(s,t);break;case"align":"string"===typeof t&&Dt(s,t);break;case"color":a.color=t;break;case"rotate":"string"===typeof t?s[o]=Tt(t):"number"===typeof t&&(s[o]=t);break;case"ariaHidden":case"aria-hidden":!0!==t&&"true"!==t&&delete l["aria-hidden"];break;default:void 0===c[o]&&(l[o]=t)}}const f=M(t,s);let u=0,d=e.id;"string"===typeof d&&(d=d.replace(/-/g,"_")),l.dangerouslySetInnerHTML={__html:R(f.body,d?()=>d+"ID"+u++:"iconifyReact")};for(let o in f.attributes)l[o]=f.attributes[o];return f.inline&&void 0===a.verticalAlign&&(a.verticalAlign="-0.125em"),i.createElement("svg",l)})(e.data,n,t._inline,t._ref)}}const Nt=i.forwardRef((function(t,e){const n=Object(o.a)(Object(o.a)({},t),{},{_ref:e,_inline:!1});return i.createElement(Lt,n)}));i.forwardRef((function(t,e){const n=Object(o.a)(Object(o.a)({},t),{},{_ref:e,_inline:!0});return i.createElement(Lt,n)}))},679:function(t,e,n){"use strict";var o=n(11),i=n(3),r=n(0),c=n(25),s=n(7),a=n(562),l=n(179),f=n(49),u=n(69),d=n(2);const p=["component","direction","spacing","divider","children"];function h(t,e){const n=r.Children.toArray(t).filter(Boolean);return n.reduce(((t,o,i)=>(t.push(o),i<n.length-1&&t.push(r.cloneElement(e,{key:"separator-".concat(i)})),t)),[])}const g=Object(f.a)("div",{name:"MuiStack",slot:"Root",overridesResolver:(t,e)=>[e.root]})((t=>{let{ownerState:e,theme:n}=t,o=Object(i.a)({display:"flex",flexDirection:"column"},Object(c.b)({theme:n},Object(c.e)({values:e.direction,breakpoints:n.breakpoints.values}),(t=>({flexDirection:t}))));if(e.spacing){const t=Object(s.a)(n),i=Object.keys(n.breakpoints.values).reduce(((t,n)=>(("object"===typeof e.spacing&&null!=e.spacing[n]||"object"===typeof e.direction&&null!=e.direction[n])&&(t[n]=!0),t)),{}),r=Object(c.e)({values:e.direction,base:i}),a=Object(c.e)({values:e.spacing,base:i});"object"===typeof r&&Object.keys(r).forEach(((t,e,n)=>{if(!r[t]){const o=e>0?r[n[e-1]]:"column";r[t]=o}}));const f=(n,o)=>{return{"& > :not(style) + :not(style)":{margin:0,["margin".concat((i=o?r[o]:e.direction,{row:"Left","row-reverse":"Right",column:"Top","column-reverse":"Bottom"}[i]))]:Object(s.c)(t,n)}};var i};o=Object(l.a)(o,Object(c.b)({theme:n},a,f))}return o=Object(c.c)(n.breakpoints,o),o})),b=r.forwardRef((function(t,e){const n=Object(u.a)({props:t,name:"MuiStack"}),r=Object(a.a)(n),{component:c="div",direction:s="column",spacing:l=0,divider:f,children:b}=r,v=Object(o.a)(r,p),m={direction:s,spacing:l};return Object(d.jsx)(g,Object(i.a)({as:c,ownerState:m,ref:e},v,{children:f?h(b,f):b}))}));e.a=b}}]);
//# sourceMappingURL=5.d31392a9.chunk.js.map