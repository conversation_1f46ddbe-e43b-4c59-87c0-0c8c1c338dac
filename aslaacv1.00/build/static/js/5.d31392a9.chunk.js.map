{"version": 3, "sources": ["../node_modules/@iconify/react/dist/iconify.mjs", "../node_modules/@mui/material/Stack/Stack.js"], "names": ["matchName", "iconDefaults", "Object", "freeze", "left", "top", "width", "height", "rotate", "vFlip", "hFlip", "fullIcon", "data", "_objectSpread", "stringToIcon", "value", "validate", "allowSimpleName", "provider", "arguments", "length", "undefined", "colonSeparated", "split", "slice", "shift", "name2", "pop", "prefix", "result", "name", "validateIcon", "dashSeparated", "join", "icon", "match", "mergeIconData", "alias", "key", "prop", "getIconData$1", "full", "getIcon", "iteration", "icons", "assign", "aliases", "item", "result2", "parent", "chars", "parseIconSet", "callback", "options", "names", "not_found", "Array", "for<PERSON>ach", "push", "keys", "iconData", "parseAliases", "isVariation", "optionalProperties", "quicklyValidateIconSet", "obj", "body", "storage$1", "create", "w", "window", "self", "_iconifyStorage", "version", "storage", "err", "getStorage", "providerStorage", "missing", "newStorage", "addIconSet", "storage2", "t", "Date", "now", "getIconFromStorage", "simpleNames", "allowSimpleNames", "allow", "getIconData", "addIcon", "addIconToStorage", "defaults", "inline", "hAlign", "vAlign", "mergeCustomisations", "defaults2", "attr", "unitsSplit", "unitsTest", "calculateSize", "size", "ratio", "precision", "Math", "ceil", "oldParts", "newParts", "code", "isNumber", "test", "num", "parseFloat", "isNaN", "preserveAspectRatio", "props", "iconToSVG", "customisations", "box", "transformations", "tempValue", "rotation", "toString", "floor", "unshift", "attributes", "viewBox", "regex", "randomPrefix", "random", "counter", "replaceIDs", "ids", "exec", "id", "newID", "escapedID", "replace", "RegExp", "setAPIModule", "getAPIModule", "createAPIConfig", "source", "resources", "path", "maxURL", "timeout", "index", "dataAfterTimeout", "configStorage", "fallBackAPISources", "fallBackAPI", "addAPIProvider", "customConfig", "config", "getAPIConfig", "concat", "mergeParams", "base", "params", "hasParams", "indexOf", "encodeURIComponent", "Error", "paramToString", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "pathCache", "fetchModule", "detectFetch", "fetch", "fetchAPIModule", "prepare", "results", "max<PERSON><PERSON><PERSON>", "maxHost<PERSON><PERSON><PERSON>", "host", "max", "url", "cache<PERSON>ey", "calculateMaxLength", "type", "send", "<PERSON><PERSON><PERSON>", "iconsList", "uri", "defaultError", "then", "response", "status", "json", "setTimeout", "shouldAbort", "catch", "callbacks", "pendingUpdates", "removeCallback", "sources", "providerCallbacks", "items", "filter", "row", "idCounter", "defaultConfig", "<PERSON><PERSON><PERSON><PERSON>", "payload", "query", "done", "resourcesCount", "startIndex", "list", "nextIndex", "startTime", "lastError", "queriesSent", "timer", "queue", "doneCallbacks", "resetTimer", "clearTimeout", "abort", "subscribe", "overwrite", "fail<PERSON><PERSON><PERSON>", "clearQueue", "execNext", "resource", "status2", "isError", "queued", "moduleResponse", "queriesPending", "initRedundancy", "cfg", "newConfig", "setConfig", "queries", "cleanup", "query<PERSON><PERSON><PERSON>", "doneCallback", "query2", "error", "find", "setIndex", "getIndex", "emptyCallback$1", "redundancyCache", "sendAPIQuery", "target", "redundancy", "api", "cached", "cachedReundancy", "getRedundancyCache", "cache", "emptyCallback", "pendingIcons", "iconsToLoad", "loaderFlags", "queueFlags", "loadedNewIcons", "providerLoaderFlags", "providerPendingUpdates", "hasPending", "<PERSON><PERSON><PERSON><PERSON>", "pending", "loaded", "updateCallbacks", "errorsCache", "loadNewIcons", "providerIconsToLoad", "providerQueueFlags", "providerPendingIcons", "sort", "icons2", "time", "console", "parsed", "store", "err2", "loadIcons", "cleanedIcons", "listToIcons", "sortedIcons", "a", "b", "localeCompare", "lastIcon", "localStorage", "sortIcons", "callCallback", "newIcons", "lastProvider", "lastPrefix", "providerNewIcons", "pendingQueue", "pendingSources", "bind", "storeCallback", "cacheVersion", "cachePrefix", "<PERSON><PERSON><PERSON>", "version<PERSON>ey", "hour", "local", "session", "count", "emptyList", "_window", "getGlobal", "setCount", "setItem", "getCount", "count2", "getItem", "total", "parseInt", "loadCache", "minTime", "load", "func", "valid", "JSON", "parse", "removeItem", "i", "destroyCache", "initCache", "storeCache", "stringify", "separator", "flipFromString", "custom", "flip", "str", "trim", "alignmentFromString", "align", "rotateFromString", "defaultValue", "units", "value2", "svgDefaults", "inlineDefaults", "document", "IconifyPreload", "preload", "added", "addCollection", "e", "IconifyProviders", "providers", "IconComponent", "React", "Component", "constructor", "super", "this", "state", "_abortLoading", "_loading", "_setData", "setState", "_checkIcon", "changed", "_icon", "iconName", "classes", "onLoad", "componentDidMount", "componentDidUpdate", "oldProps", "componentWillUnmount", "render", "children", "createElement", "newProps", "className", "ref", "defaultProps", "style", "componentProps", "color", "localCounter", "dangerouslySetInnerHTML", "__html", "verticalAlign", "_inline", "_ref", "Icon", "forwardRef", "_excluded", "joinChildren", "childrenA<PERSON>y", "toArray", "Boolean", "reduce", "output", "child", "StackRoot", "styled", "slot", "overridesResolver", "styles", "root", "ownerState", "theme", "_extends", "display", "flexDirection", "handleBreakpoints", "resolveBreakpointValues", "values", "direction", "breakpoints", "propValue", "spacing", "transformer", "createUnarySpacing", "acc", "breakpoint", "directionV<PERSON>ues", "spacingValues", "previousDirectionValue", "styleFromPropValue", "margin", "column", "getValue", "deepmerge", "mergeBreakpointsInOrder", "<PERSON><PERSON>", "inProps", "themeProps", "useThemeProps", "extendSxProp", "component", "divider", "other", "_objectWithoutPropertiesLoose", "_jsx", "as"], "mappings": "uJAEA,MAAMA,EAAY,2BACZC,EAAeC,OAAOC,OAAO,CACjCC,KAAM,EACNC,IAAK,EACLC,MAAO,GACPC,OAAQ,GACRC,OAAQ,EACRC,OAAO,EACPC,OAAO,IAET,SAASC,EAASC,GAChB,OAAAC,wBAAA,GAAYZ,GAAiBW,EAC/B,CAEA,MAAME,EAAe,SAACC,EAAOC,EAAUC,GAAmC,IAAlBC,EAAQC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACjE,MAAMG,EAAiBP,EAAMQ,MAAM,KACnC,GAA0B,MAAtBR,EAAMS,MAAM,EAAG,GAAY,CAC7B,GAAIF,EAAeF,OAAS,GAAKE,EAAeF,OAAS,EACvD,OAAO,KAETF,EAAWI,EAAeG,QAAQD,MAAM,EAC1C,CACA,GAAIF,EAAeF,OAAS,IAAME,EAAeF,OAC/C,OAAO,KAET,GAAIE,EAAeF,OAAS,EAAG,CAC7B,MAAMM,EAAQJ,EAAeK,MACvBC,EAASN,EAAeK,MACxBE,EAAS,CACbX,SAAUI,EAAeF,OAAS,EAAIE,EAAe,GAAKJ,EAC1DU,SACAE,KAAMJ,GAER,OAAOV,IAAae,EAAaF,GAAU,KAAOA,CACpD,CACA,MAAMC,EAAOR,EAAe,GACtBU,EAAgBF,EAAKP,MAAM,KACjC,GAAIS,EAAcZ,OAAS,EAAG,CAC5B,MAAMS,EAAS,CACbX,WACAU,OAAQI,EAAcP,QACtBK,KAAME,EAAcC,KAAK,MAE3B,OAAOjB,IAAae,EAAaF,GAAU,KAAOA,CACpD,CACA,GAAIZ,GAAgC,KAAbC,EAAiB,CACtC,MAAMW,EAAS,CACbX,WACAU,OAAQ,GACRE,QAEF,OAAOd,IAAae,EAAaF,EAAQZ,GAAmB,KAAOY,CACrE,CACA,OAAO,IACT,EACME,EAAeA,CAACG,EAAMjB,MACrBiB,KAGwB,KAAlBA,EAAKhB,WAAmBgB,EAAKhB,SAASiB,MAAMnC,MAAgBiB,GAAmC,KAAhBiB,EAAKN,QAAiBM,EAAKN,OAAOO,MAAMnC,MAAekC,EAAKJ,KAAKK,MAAMnC,IAGnK,SAASoC,EAAcF,EAAMG,GAC3B,MAAMR,EAAMhB,YAAA,GAAQqB,GACpB,IAAK,MAAMI,KAAOrC,EAAc,CAC9B,MAAMsC,EAAOD,EACb,QAAoB,IAAhBD,EAAME,GAAkB,CAC1B,MAAMxB,EAAQsB,EAAME,GACpB,QAAqB,IAAjBV,EAAOU,GAAkB,CAC3BV,EAAOU,GAAQxB,EACf,QACF,CACA,OAAQwB,GACN,IAAK,SACHV,EAAOU,IAASV,EAAOU,GAAQxB,GAAS,EACxC,MACF,IAAK,QACL,IAAK,QACHc,EAAOU,GAAQxB,IAAUc,EAAOU,GAChC,MACF,QACEV,EAAOU,GAAQxB,EAErB,CACF,CACA,OAAOc,CACT,CAEA,SAASW,EAAc5B,EAAMkB,GAAoB,IAAdW,EAAItB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,IAAAA,UAAA,GACrC,SAASuB,EAAQhB,EAAOiB,GACtB,QAA0B,IAAtB/B,EAAKgC,MAAMlB,GACb,OAAOxB,OAAO2C,OAAO,CAAC,EAAGjC,EAAKgC,MAAMlB,IAEtC,GAAIiB,EAAY,EACd,OAAO,KAET,MAAMG,EAAUlC,EAAKkC,QACrB,GAAIA,QAA8B,IAAnBA,EAAQpB,GAAmB,CACxC,MAAMqB,EAAOD,EAAQpB,GACfsB,EAAUN,EAAQK,EAAKE,OAAQN,EAAY,GACjD,OAAIK,EACKZ,EAAcY,EAASD,GAEzBC,CACT,CACA,MAAME,EAAQtC,EAAKsC,MACnB,OAAKP,GAAaO,QAA0B,IAAjBA,EAAMxB,GACxBgB,EAAQQ,EAAMxB,GAAQiB,EAAY,GAEpC,IACT,CACA,MAAMd,EAASa,EAAQZ,EAAM,GAC7B,GAAID,EACF,IAAK,MAAMS,KAAOrC,OACI,IAAhB4B,EAAOS,SAAiC,IAAd1B,EAAK0B,KACjCT,EAAOS,GAAO1B,EAAK0B,IAIzB,OAAOT,GAAUY,EAAO9B,EAASkB,GAAUA,CAC7C,CAUA,SAASsB,EAAavC,EAAMwC,EAAUC,GACpCA,EAAUA,GAAW,CAAC,EACtB,MAAMC,EAAQ,GACd,GAAoB,kBAAT1C,GAA2C,kBAAfA,EAAKgC,MAC1C,OAAOU,EAEL1C,EAAK2C,qBAAqBC,OAC5B5C,EAAK2C,UAAUE,SAAS3B,IACtBsB,EAAStB,EAAM,MACfwB,EAAMI,KAAK5B,EAAK,IAGpB,MAAMc,EAAQhC,EAAKgC,MACnB1C,OAAOyD,KAAKf,GAAOa,SAAS3B,IAC1B,MAAM8B,EAAWpB,EAAc5B,EAAMkB,GAAM,GACvC8B,IACFR,EAAStB,EAAM8B,GACfN,EAAMI,KAAK5B,GACb,IAEF,MAAM+B,EAAeR,EAAQP,SAAW,MACxC,GAAqB,SAAjBe,GAAmD,kBAAjBjD,EAAKkC,QAAsB,CAC/D,MAAMA,EAAUlC,EAAKkC,QACrB5C,OAAOyD,KAAKb,GAASW,SAAS3B,IAC5B,GAAqB,eAAjB+B,GAhCV,SAAqBd,GACnB,IAAK,MAAMT,KAAOrC,EAChB,QAAkB,IAAd8C,EAAKT,GACP,OAAO,EAGX,OAAO,CACT,CAyB2CwB,CAAYhB,EAAQhB,IACvD,OAEF,MAAM8B,EAAWpB,EAAc5B,EAAMkB,GAAM,GACvC8B,IACFR,EAAStB,EAAM8B,GACfN,EAAMI,KAAK5B,GACb,GAEJ,CACA,OAAOwB,CACT,CAEA,MAAMS,EAAqB,CACzB7C,SAAU,SACV4B,QAAS,SACTS,UAAW,UAEb,IAAK,MAAMhB,MAAQtC,EACjB8D,EAAmBxB,WAAetC,EAAasC,IAEjD,SAASyB,EAAuBC,GAC9B,GAAmB,kBAARA,GAA4B,OAARA,EAC7B,OAAO,KAET,MAAMrD,EAAOqD,EACb,GAA2B,kBAAhBrD,EAAKgB,SAAwBqC,EAAIrB,OAA8B,kBAAdqB,EAAIrB,MAC9D,OAAO,KAET,IAAK,MAAML,KAAQwB,EACjB,QAAkB,IAAdE,EAAI1B,WAA2B0B,EAAI1B,KAAUwB,EAAmBxB,GAClE,OAAO,KAGX,MAAMK,EAAQhC,EAAKgC,MACnB,IAAK,MAAMd,KAAQc,EAAO,CACxB,MAAMV,EAAOU,EAAMd,GACnB,IAAKA,EAAKK,MAAMnC,IAAmC,kBAAdkC,EAAKgC,KACxC,OAAO,KAET,IAAK,MAAM3B,KAAQtC,EACjB,QAAmB,IAAfiC,EAAKK,WAA2BL,EAAKK,YAAiBtC,EAAasC,GACrE,OAAO,IAGb,CACA,MAAMO,EAAUlC,EAAKkC,QACrB,GAAIA,EACF,IAAK,MAAMhB,KAAQgB,EAAS,CAC1B,MAAMZ,EAAOY,EAAQhB,GACfmB,EAASf,EAAKe,OACpB,IAAKnB,EAAKK,MAAMnC,IAAgC,kBAAXiD,IAAwBL,EAAMK,KAAYH,EAAQG,GACrF,OAAO,KAET,IAAK,MAAMV,KAAQtC,EACjB,QAAmB,IAAfiC,EAAKK,WAA2BL,EAAKK,YAAiBtC,EAAasC,GACrE,OAAO,IAGb,CAEF,OAAO3B,CACT,CAGA,IAAIuD,EAA4BjE,OAAOkE,OAAO,MAC9C,IACE,MAAMC,EAAIC,QAAUC,KAChBF,GAJiB,IAIZA,EAAEG,gBAAgBC,UACzBN,EAAYE,EAAEG,gBAAgBE,QAGlC,CADE,MAAOC,IACT,CAqBA,SAASC,EAAW1D,EAAUU,QACA,IAAxBuC,EAAUjD,KACZiD,EAAUjD,GAA4BhB,OAAOkE,OAAO,OAEtD,MAAMS,EAAkBV,EAAUjD,GAIlC,YAHgC,IAA5B2D,EAAgBjD,KAClBiD,EAAgBjD,GAdpB,SAAoBV,EAAUU,GAC5B,MAAO,CACLV,WACAU,SACAgB,MAAuB1C,OAAOkE,OAAO,MACrCU,QAAyB5E,OAAOkE,OAAO,MAE3C,CAO8BW,CAAW7D,EAAUU,IAE1CiD,EAAgBjD,EACzB,CACA,SAASoD,EAAWC,EAAUrE,GAC5B,IAAKoD,EAAuBpD,GAC1B,MAAO,GAET,MAAMsE,EAAIC,KAAKC,MACf,OAAOjC,EAAavC,GAAM,CAACkB,EAAMI,KAC3BA,EACF+C,EAASrC,MAAMd,GAAQI,EAEvB+C,EAASH,QAAQhD,GAAQoD,CAC3B,GAEJ,CAWA,SAASG,EAAmBJ,EAAUnD,GACpC,MAAMf,EAAQkE,EAASrC,MAAMd,GAC7B,YAAiB,IAAVf,EAAmB,KAAOA,CACnC,CAyBA,IAAIuE,GAAc,EAClB,SAASC,EAAiBC,GAIxB,MAHqB,mBAAVA,IACTF,EAAcE,GAETF,CACT,CACA,SAASG,EAAY3D,GACnB,MAAMI,EAAuB,kBAATJ,EAAoBhB,EAAagB,GAAM,EAAMwD,GAAexD,EAChF,OAAOI,EAAOmD,EAAmBT,EAAW1C,EAAKhB,SAAUgB,EAAKN,QAASM,EAAKJ,MAAQ,IACxF,CACA,SAAS4D,EAAQ5D,EAAMlB,GACrB,MAAMsB,EAAOpB,EAAagB,GAAM,EAAMwD,GACtC,IAAKpD,EACH,OAAO,EAGT,OAvDF,SAA0B+C,EAAUnD,EAAMI,GACxC,IACE,GAAyB,kBAAdA,EAAKgC,KAEd,OADAe,EAASrC,MAAMd,GAAQ5B,OAAOC,OAAOQ,EAASuB,KACvC,CAGX,CADE,MAAOyC,IACT,CACA,OAAO,CACT,CA8CSgB,CADSf,EAAW1C,EAAKhB,SAAUgB,EAAKN,QACdM,EAAKJ,KAAMlB,EAC9C,CAsCA,MAAMgF,EAAW1F,OAAOC,OAAO,CAC7B0F,QAAQ,EACRvF,MAAO,KACPC,OAAQ,KACRuF,OAAQ,SACRC,OAAQ,SACRvE,OAAO,EACPd,OAAO,EACPD,OAAO,EACPD,OAAQ,IAEV,SAASwF,EAAoBC,EAAWlD,GACtC,MAAMlB,EAAS,CAAC,EAChB,IAAK,MAAMS,KAAO2D,EAAW,CAC3B,MAAMC,EAAO5D,EAEb,GADAT,EAAOqE,GAAQD,EAAUC,QACN,IAAfnD,EAAKmD,GACP,SAEF,MAAMnF,EAAQgC,EAAKmD,GACnB,OAAQA,GACN,IAAK,SACL,IAAK,QACkB,mBAAVnF,IACTc,EAAOqE,GAAQnF,GAEjB,MACF,IAAK,QACL,IAAK,SACW,IAAVA,IACFc,EAAOqE,IAASrE,EAAOqE,IAEzB,MACF,IAAK,SACL,IAAK,SACkB,kBAAVnF,GAAgC,KAAVA,IAC/Bc,EAAOqE,GAAQnF,GAEjB,MACF,IAAK,QACL,IAAK,UACkB,kBAAVA,GAAgC,KAAVA,GAAiC,kBAAVA,GAAsBA,GAAmB,OAAVA,KACrFc,EAAOqE,GAAQnF,GAEjB,MACF,IAAK,SACkB,kBAAVA,IACTc,EAAOqE,IAASnF,GAIxB,CACA,OAAOc,CACT,CAEA,MAAMsE,EAAa,4BACbC,EAAY,4BAClB,SAASC,EAAcC,EAAMC,EAAOC,GAClC,GAAc,IAAVD,EACF,OAAOD,EAGT,GADAE,OAA0B,IAAdA,EAAuB,IAAMA,EACrB,kBAATF,EACT,OAAOG,KAAKC,KAAKJ,EAAOC,EAAQC,GAAaA,EAE/C,GAAoB,kBAATF,EACT,OAAOA,EAET,MAAMK,EAAWL,EAAK/E,MAAM4E,GAC5B,GAAiB,OAAbQ,IAAsBA,EAASvF,OACjC,OAAOkF,EAET,MAAMM,EAAW,GACjB,IAAIC,EAAOF,EAASlF,QAChBqF,EAAWV,EAAUW,KAAKF,GAC9B,OAAa,CACX,GAAIC,EAAU,CACZ,MAAME,EAAMC,WAAWJ,GACnBK,MAAMF,GACRJ,EAASlD,KAAKmD,GAEdD,EAASlD,KAAK+C,KAAKC,KAAKM,EAAMT,EAAQC,GAAaA,EAEvD,MACEI,EAASlD,KAAKmD,GAGhB,GADAA,EAAOF,EAASlF,aACH,IAAToF,EACF,OAAOD,EAAS3E,KAAK,IAEvB6E,GAAYA,CACd,CACF,CAEA,SAASK,EAAoBC,GAC3B,IAAIvF,EAAS,GACb,OAAQuF,EAAMtB,QACZ,IAAK,OACHjE,GAAU,OACV,MACF,IAAK,QACHA,GAAU,OACV,MACF,QACEA,GAAU,OAEd,OAAQuF,EAAMrB,QACZ,IAAK,MACHlE,GAAU,OACV,MACF,IAAK,SACHA,GAAU,OACV,MACF,QACEA,GAAU,OAGd,OADAA,GAAUuF,EAAM5F,MAAQ,SAAW,QAC5BK,CACT,CACA,SAASwF,EAAUnF,EAAMoF,GACvB,MAAMC,EAAM,CACVnH,KAAM8B,EAAK9B,KACXC,IAAK6B,EAAK7B,IACVC,MAAO4B,EAAK5B,MACZC,OAAQ2B,EAAK3B,QAEf,IAqDID,EAAOC,EArDP2D,EAAOhC,EAAKgC,KAChB,CAAChC,EAAMoF,GAAgB7D,SAAS2D,IAC9B,MAAMI,EAAkB,GAClB9G,EAAQ0G,EAAM1G,MACdD,EAAQ2G,EAAM3G,MACpB,IAcIgH,EAdAC,EAAWN,EAAM5G,OAmBrB,OAlBIE,EACED,EACFiH,GAAY,GAEZF,EAAgB9D,KAAK,cAAgB6D,EAAIjH,MAAQiH,EAAInH,MAAMuH,WAAa,KAAO,EAAIJ,EAAIlH,KAAKsH,WAAa,KACzGH,EAAgB9D,KAAK,eACrB6D,EAAIlH,IAAMkH,EAAInH,KAAO,GAEdK,IACT+G,EAAgB9D,KAAK,cAAgB,EAAI6D,EAAInH,MAAMuH,WAAa,KAAOJ,EAAIhH,OAASgH,EAAIlH,KAAKsH,WAAa,KAC1GH,EAAgB9D,KAAK,eACrB6D,EAAIlH,IAAMkH,EAAInH,KAAO,GAGnBsH,EAAW,IACbA,GAAuC,EAA3BjB,KAAKmB,MAAMF,EAAW,IAEpCA,GAAsB,EACdA,GACN,KAAK,EACHD,EAAYF,EAAIhH,OAAS,EAAIgH,EAAIlH,IACjCmH,EAAgBK,QAAQ,aAAeJ,EAAUE,WAAa,IAAMF,EAAUE,WAAa,KAC3F,MACF,KAAK,EACHH,EAAgBK,QAAQ,eAAiBN,EAAIjH,MAAQ,EAAIiH,EAAInH,MAAMuH,WAAa,KAAOJ,EAAIhH,OAAS,EAAIgH,EAAIlH,KAAKsH,WAAa,KAC9H,MACF,KAAK,EACHF,EAAYF,EAAIjH,MAAQ,EAAIiH,EAAInH,KAChCoH,EAAgBK,QAAQ,cAAgBJ,EAAUE,WAAa,IAAMF,EAAUE,WAAa,KAG5FD,EAAW,IAAM,IACF,IAAbH,EAAInH,MAA0B,IAAZmH,EAAIlH,MACxBoH,EAAYF,EAAInH,KAChBmH,EAAInH,KAAOmH,EAAIlH,IACfkH,EAAIlH,IAAMoH,GAERF,EAAIjH,QAAUiH,EAAIhH,SACpBkH,EAAYF,EAAIjH,MAChBiH,EAAIjH,MAAQiH,EAAIhH,OAChBgH,EAAIhH,OAASkH,IAGbD,EAAgBpG,SAClB8C,EAAO,iBAAmBsD,EAAgBvF,KAAK,KAAO,KAAOiC,EAAO,OACtE,IAG2B,OAAzBoD,EAAehH,OAA4C,OAA1BgH,EAAe/G,QAClDA,EAAS,MACTD,EAAQ+F,EAAc9F,EAAQgH,EAAIjH,MAAQiH,EAAIhH,SACZ,OAAzB+G,EAAehH,OAA4C,OAA1BgH,EAAe/G,QACzDD,EAAQgH,EAAehH,MACvBC,EAAS+G,EAAe/G,QACW,OAA1B+G,EAAe/G,QACxBA,EAAS+G,EAAe/G,OACxBD,EAAQ+F,EAAc9F,EAAQgH,EAAIjH,MAAQiH,EAAIhH,UAE9CD,EAAQgH,EAAehH,MACvBC,EAAS8F,EAAc/F,EAAOiH,EAAIhH,OAASgH,EAAIjH,QAEnC,SAAVA,IACFA,EAAQiH,EAAIjH,OAEC,SAAXC,IACFA,EAASgH,EAAIhH,QAEfD,EAAyB,kBAAVA,EAAqBA,EAAQA,EAAMqH,WAAa,GAC/DpH,EAA2B,kBAAXA,EAAsBA,EAASA,EAAOoH,WAAa,GACnE,MAAM9F,EAAS,CACbiG,WAAY,CACVxH,QACAC,SACA4G,oBAAqBA,EAAoBG,GACzCS,QAASR,EAAInH,KAAKuH,WAAa,IAAMJ,EAAIlH,IAAIsH,WAAa,IAAMJ,EAAIjH,MAAMqH,WAAa,IAAMJ,EAAIhH,OAAOoH,YAE1GzD,QAKF,OAHIoD,EAAezB,SACjBhE,EAAOgE,QAAS,GAEXhE,CACT,CAMA,MAAMmG,EAAQ,gBACRC,EAAe,YAAc9C,KAAKC,MAAMuC,SAAS,KAAuB,SAAhBlB,KAAKyB,SAAsB,GAAGP,SAAS,IACrG,IAAIQ,EAAU,EACd,SAASC,EAAWlE,GAA6B,IAAvBtC,EAAMT,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG8G,EACjC,MAAMI,EAAM,GACZ,IAAIlG,EACJ,KAAOA,EAAQ6F,EAAMM,KAAKpE,IACxBmE,EAAI3E,KAAKvB,EAAM,IAEjB,OAAKkG,EAAIjH,QAGTiH,EAAI5E,SAAS8E,IACX,MAAMC,EAA0B,oBAAX5G,EAAwBA,EAAO2G,GAAM3G,GAAUuG,KAAWR,WACzEc,EAAYF,EAAGG,QAAQ,sBAAuB,QACpDxE,EAAOA,EAAKwE,QAAQ,IAAIC,OAAO,WAAaF,EAAY,mBAAoB,KAAM,KAAOD,EAAQ,KAAK,IAEjGtE,GAPEA,CAQX,CAEA,MAAMQ,EAA0BxE,OAAOkE,OAAO,MAC9C,SAASwE,EAAa1H,EAAU6B,GAC9B2B,EAAQxD,GAAY6B,CACtB,CACA,SAAS8F,EAAa3H,GACpB,OAAOwD,EAAQxD,IAAawD,EAAQ,GACtC,CAEA,SAASoE,EAAgBC,GACvB,IAAIC,EACJ,GAAgC,kBAArBD,EAAOC,UAChBA,EAAY,CAACD,EAAOC,gBAGpB,GADAA,EAAYD,EAAOC,YACbA,aAAqBxF,SAAWwF,EAAU5H,OAC9C,OAAO,KAaX,MAVe,CACb4H,YACAC,UAAsB,IAAhBF,EAAOE,KAAkB,IAAMF,EAAOE,KAC5CC,OAAQH,EAAOG,OAASH,EAAOG,OAAS,IACxC1I,OAAQuI,EAAOvI,OAASuI,EAAOvI,OAAS,IACxC2I,QAASJ,EAAOI,QAAUJ,EAAOI,QAAU,IAC3CjB,QAA0B,IAAlBa,EAAOb,OACfkB,MAAOL,EAAOK,MAAQL,EAAOK,MAAQ,EACrCC,kBAA8C,IAA5BN,EAAOM,iBAG7B,CACA,MAAMC,EAAgCpJ,OAAOkE,OAAO,MAC9CmF,EAAqB,CACzB,4BACA,0BAEIC,EAAc,GACpB,KAAOD,EAAmBnI,OAAS,GACC,IAA9BmI,EAAmBnI,QAGjBqF,KAAKyB,SAAW,GAFpBsB,EAAY9F,KAAK6F,EAAmB9H,SAKlC+H,EAAY9F,KAAK6F,EAAmB5H,OAO1C,SAAS8H,EAAevI,EAAUwI,GAChC,MAAMC,EAASb,EAAgBY,GAC/B,OAAe,OAAXC,IAGJL,EAAcpI,GAAYyI,GACnB,EACT,CACA,SAASC,EAAa1I,GACpB,OAAOoI,EAAcpI,EACvB,CAbAoI,EAAc,IAAMR,EAAgB,CAClCE,UAAW,CAAC,8BAA8Ba,OAAOL,KAiBnD,MAAMM,EAAcA,CAACC,EAAMC,KACzB,IAAInI,EAASkI,EAAME,GAAqC,IAAzBpI,EAAOqI,QAAQ,KAuB9C,OAVAhK,OAAOyD,KAAKqG,GAAQvG,SAASnB,IAC3B,IAAIvB,EACJ,IACEA,EAfJ,SAAuBA,GACrB,cAAeA,GACb,IAAK,UACH,OAAOA,EAAQ,OAAS,QAC1B,IAAK,SAEL,IAAK,SACH,OAAOoJ,mBAAmBpJ,GAC5B,QACE,MAAM,IAAIqJ,MAAM,qBAEtB,CAIYC,CAAcL,EAAO1H,GAG/B,CAFE,MAAOqC,IACP,MACF,CACA9C,IAAWoI,EAAY,IAAM,KAAOE,mBAAmB7H,GAAO,IAAMvB,EACpEkJ,GAAY,CAAI,IAEXpI,CAAM,EAGTyI,EAAiB,CAAC,EAClBC,EAAY,CAAC,EAYnB,IAAIC,EAXgBC,MAClB,IAAIrH,EACJ,IAEE,GADAA,EAAWsH,MACa,oBAAbtH,EACT,OAAOA,CAGX,CADE,MAAOuB,IACT,CACA,OAAO,IAAI,EAEK8F,GA8ElB,MAkDME,EAAiB,CACrBC,QA/FcA,CAAC1J,EAAUU,EAAQgB,KACjC,MAAMiI,EAAU,GAChB,IAAIC,EAAYR,EAAe1I,QACb,IAAdkJ,IACFA,EA/BJ,SAA4B5J,EAAUU,GACpC,MAAM+H,EAASC,EAAa1I,GAC5B,IAAKyI,EACH,OAAO,EAET,IAAI9H,EACJ,GAAK8H,EAAOT,OAEL,CACL,IAAI6B,EAAgB,EACpBpB,EAAOX,UAAUvF,SAASV,IACxB,MAAMiI,EAAOjI,EACbgI,EAAgBtE,KAAKwE,IAAIF,EAAeC,EAAK5J,OAAO,IAEtD,MAAM8J,EAAMpB,EAAYlI,EAAS,QAAS,CACxCgB,MAAO,KAETf,EAAS8H,EAAOT,OAAS6B,EAAgBpB,EAAOV,KAAK7H,OAAS8J,EAAI9J,MACpE,MAXES,EAAS,EAYX,MAAMsJ,EAAWjK,EAAW,IAAMU,EAGlC,OAFA2I,EAAUrJ,GAAYyI,EAAOV,KAC7BqB,EAAea,GAAYtJ,EACpBA,CACT,CAQgBuJ,CAAmBlK,EAAUU,IAE3C,MAAMyJ,EAAO,QACb,IAAItI,EAAO,CACTsI,OACAnK,WACAU,SACAgB,MAAO,IAELxB,EAAS,EAgBb,OAfAwB,EAAMa,SAAQ,CAAC3B,EAAMsH,KACnBhI,GAAUU,EAAKV,OAAS,EACpBA,GAAU0J,GAAa1B,EAAQ,IACjCyB,EAAQnH,KAAKX,GACbA,EAAO,CACLsI,OACAnK,WACAU,SACAgB,MAAO,IAETxB,EAASU,EAAKV,QAEhB2B,EAAKH,MAAMc,KAAK5B,EAAK,IAEvB+I,EAAQnH,KAAKX,GACN8H,CAAO,EAmEdS,KApDWA,CAACN,EAAMhB,EAAQ5G,KAC1B,IAAKoH,EAEH,YADApH,EAAS,QAAS,KAGpB,IAAI6F,EAlBN,SAAiB/H,GACf,GAAwB,kBAAbA,EAAuB,CAChC,QAA4B,IAAxBqJ,EAAUrJ,GAAsB,CAClC,MAAMyI,EAASC,EAAa1I,GAC5B,IAAKyI,EACH,MAAO,IAETY,EAAUrJ,GAAYyI,EAAOV,IAC/B,CACA,OAAOsB,EAAUrJ,EACnB,CACA,MAAO,GACT,CAMaqK,CAAQvB,EAAO9I,UAC1B,OAAQ8I,EAAOqB,MACb,IAAK,QAAS,CACZ,MAAMzJ,EAASoI,EAAOpI,OAEhB4J,EADQxB,EAAOpH,MACGX,KAAK,KAC7BgH,GAAQa,EAAYlI,EAAS,QAAS,CACpCgB,MAAO4I,IAET,KACF,CACA,IAAK,SAAU,CACb,MAAMC,EAAMzB,EAAOyB,IACnBxC,GAA4B,MAApBwC,EAAIjK,MAAM,EAAG,GAAaiK,EAAIjK,MAAM,GAAKiK,EACjD,KACF,CACA,QAEE,YADArI,EAAS,QAAS,KAGtB,IAAIsI,EAAe,IACnBlB,EAAYQ,EAAO/B,GAAM0C,MAAMC,IAC7B,MAAMC,EAASD,EAASC,OACxB,GAAe,MAAXA,EAOJ,OADAH,EAAe,IACRE,EAASE,OANdC,YAAW,KACT3I,EA7ER,SAAqByI,GACnB,OAAkB,MAAXA,CACT,CA2EiBG,CAAYH,GAAU,QAAU,OAAQA,EAAO,GAKtC,IACrBF,MAAM/K,IACa,kBAATA,GAA8B,OAATA,EAMhCmL,YAAW,KACT3I,EAAS,UAAWxC,EAAK,IANzBmL,YAAW,KACT3I,EAAS,OAAQsI,EAAa,GAMhC,IACDO,OAAM,KACP7I,EAAS,OAAQsI,EAAa,GAC9B,GA8DJ,MAAMQ,EAA4BhM,OAAOkE,OAAO,MAC1C+H,EAAiCjM,OAAOkE,OAAO,MACrD,SAASgI,EAAeC,EAAS9D,GAC/B8D,EAAQ5I,SAASsF,IACf,MAAM7H,EAAW6H,EAAO7H,SACxB,QAA4B,IAAxBgL,EAAUhL,GACZ,OAEF,MAAMoL,EAAoBJ,EAAUhL,GAC9BU,EAASmH,EAAOnH,OAChB2K,EAAQD,EAAkB1K,GAC5B2K,IACFD,EAAkB1K,GAAU2K,EAAMC,QAAQC,GAAQA,EAAIlE,KAAOA,IAC/D,GAEJ,CA4DA,IAAImE,EAAY,EA4ChB,IAAIC,EAAgB,CAClB3D,UAAW,GACXI,MAAO,EACPD,QAAS,IACT3I,OAAQ,IACR0H,QAAQ,EACRmB,kBAAkB,GAIpB,SAASuD,EAAUjD,EAAQkD,EAASC,EAAOC,GACzC,MAAMC,EAAiBrD,EAAOX,UAAU5H,OAClC6L,EAAatD,EAAOzB,OAASzB,KAAKmB,MAAMnB,KAAKyB,SAAW8E,GAAkBrD,EAAOP,MACvF,IAAIJ,EACJ,GAAIW,EAAOzB,OAAQ,CACjB,IAAIgF,EAAOvD,EAAOX,UAAUxH,MAAM,GAElC,IADAwH,EAAY,GACLkE,EAAK9L,OAAS,GAAG,CACtB,MAAM+L,EAAY1G,KAAKmB,MAAMnB,KAAKyB,SAAWgF,EAAK9L,QAClD4H,EAAUtF,KAAKwJ,EAAKC,IACpBD,EAAOA,EAAK1L,MAAM,EAAG2L,GAAWtD,OAAOqD,EAAK1L,MAAM2L,EAAY,GAChE,CACAnE,EAAYA,EAAUa,OAAOqD,EAC/B,MACElE,EAAYW,EAAOX,UAAUxH,MAAMyL,GAAYpD,OAAOF,EAAOX,UAAUxH,MAAM,EAAGyL,IAElF,MAAMG,EAAYjI,KAAKC,MACvB,IAEIiI,EAFAxB,EAAS,UACTyB,EAAc,EAEdC,EAAQ,KACRC,EAAQ,GACRC,EAAgB,GAIpB,SAASC,IACHH,IACFI,aAAaJ,GACbA,EAAQ,KAEZ,CACA,SAASK,IACQ,YAAX/B,IACFA,EAAS,WAEX6B,IACAF,EAAM/J,SAASV,IACO,YAAhBA,EAAK8I,SACP9I,EAAK8I,OAAS,UAChB,IAEF2B,EAAQ,EACV,CACA,SAASK,EAAUzK,EAAU0K,GACvBA,IACFL,EAAgB,IAEM,oBAAbrK,GACTqK,EAAc/J,KAAKN,EAEvB,CAYA,SAAS2K,IACPlC,EAAS,SACT4B,EAAchK,SAASL,IACrBA,OAAS,EAAQiK,EAAU,GAE/B,CACA,SAASW,IACPR,EAAM/J,SAASV,IACO,YAAhBA,EAAK8I,SACP9I,EAAK8I,OAAS,UAChB,IAEF2B,EAAQ,EACV,CA4CA,SAASS,IACP,GAAe,YAAXpC,EACF,OAEF6B,IACA,MAAMQ,EAAWlF,EAAUvH,QAC3B,QAAiB,IAAbyM,EACF,OAAIV,EAAMpM,YACRmM,EAAQxB,YAAW,KACjB2B,IACe,YAAX7B,IACFmC,IACAD,IACF,GACCpE,EAAOR,eAGZ4E,IAGF,MAAMhL,EAAO,CACX8I,OAAQ,UACRqC,WACA9K,SAAUA,CAAC+K,EAASvN,MAlExB,SAAwBmC,EAAM6I,EAAUhL,GACtC,MAAMwN,EAAuB,YAAbxC,EAEhB,OADA4B,EAAQA,EAAMhB,QAAQ6B,GAAWA,IAAWtL,IACpC8I,GACN,IAAK,UACH,MACF,IAAK,SACH,GAAIuC,IAAYzE,EAAON,iBACrB,OAEF,MACF,QACE,OAEJ,GAAiB,UAAbuC,EAGF,OAFAyB,EAAYzM,OACZmN,IAGF,GAAIK,EASF,OARAf,EAAYzM,OACP4M,EAAMpM,SACJ4H,EAAU5H,OAGb6M,IAFAF,MASN,GAFAL,IACAM,KACKrE,EAAOzB,OAAQ,CAClB,MAAMkB,EAAQO,EAAOX,UAAUkB,QAAQnH,EAAKmL,WAC7B,IAAX9E,GAAgBA,IAAUO,EAAOP,QACnCO,EAAOP,MAAQA,EAEnB,CACAyC,EAAS,YACT4B,EAAchK,SAASL,IACrBA,EAASxC,EAAK,GAElB,CAyBM0N,CAAevL,EAAMoL,EAASvN,EAAK,GAGvC4M,EAAM9J,KAAKX,GACXuK,IACAC,EAAQxB,WAAWkC,EAAUtE,EAAOnJ,QACpCsM,EAAMoB,EAAUrB,EAAS9J,EAAKK,SAChC,CAEA,MAlIoB,oBAAT2J,GACTU,EAAc/J,KAAKqJ,GAgIrBhB,WAAWkC,GApGX,WACE,MAAO,CACLb,YACAP,UACAhB,SACAyB,cACAiB,eAAgBf,EAAMpM,OACtByM,YACAD,QAEJ,CA4FF,CAkBA,SAASY,GAAeC,GACtB,MAAM9E,EAhBR,SAAmBA,GACjB,GAAsB,kBAAXA,GAAmD,kBAArBA,EAAOX,aAA4BW,EAAOX,qBAAqBxF,SAAWmG,EAAOX,UAAU5H,OAClI,MAAM,IAAIgJ,MAAM,oCAElB,MAAMsE,EAA4BxO,OAAOkE,OAAO,MAChD,IAAI9B,EACJ,IAAKA,KAAOqK,OACU,IAAhBhD,EAAOrH,GACToM,EAAUpM,GAAOqH,EAAOrH,GAExBoM,EAAUpM,GAAOqK,EAAcrK,GAGnC,OAAOoM,CACT,CAEiBC,CAAUF,GACzB,IAAIG,EAAU,GACd,SAASC,IACPD,EAAUA,EAAQpC,QAAQzJ,GAA2B,YAAlBA,IAAO8I,QAC5C,CA0BA,MATiB,CACfiB,MAjBF,SAAeD,EAASiC,EAAeC,GACrC,MAAMC,EAASpC,EAAUjD,EAAQkD,EAASiC,GAAe,CAAClO,EAAMqO,KAC9DJ,IACIE,GACFA,EAAanO,EAAMqO,EACrB,IAGF,OADAL,EAAQlL,KAAKsL,GACNA,CACT,EASEE,KARF,SAAc9L,GACZ,MAAMvB,EAAS+M,EAAQM,MAAMnO,GACpBqC,EAASrC,KAElB,YAAkB,IAAXc,EAAoBA,EAAS,IACtC,EAIEsN,SAAW/F,IACTO,EAAOP,MAAQA,CAAK,EAEtBgG,SAAUA,IAAMzF,EAAOP,MACvByF,UAGJ,CAEA,SAASQ,KACT,CACA,MAAMC,GAAkCpP,OAAOkE,OAAO,MAgBtD,SAASmL,GAAaC,EAAQ1C,EAAO1J,GACnC,IAAIqM,EACAnE,EACJ,GAAsB,kBAAXkE,EAAqB,CAC9B,MAAME,EAAM7G,EAAa2G,GACzB,IAAKE,EAEH,OADAtM,OAAS,EAAQ,KACViM,GAET/D,EAAOoE,EAAIpE,KACX,MAAMqE,EAzBV,SAA4BzO,GAC1B,QAAkC,IAA9BoO,GAAgBpO,GAAsB,CACxC,MAAMyI,EAASC,EAAa1I,GAC5B,IAAKyI,EACH,OAEF,MACMiG,EAAkB,CACtBjG,SACA8F,WAHiBjB,GAAe7E,IAKlC2F,GAAgBpO,GAAY0O,CAC9B,CACA,OAAON,GAAgBpO,EACzB,CAWmB2O,CAAmBL,GAC9BG,IACFF,EAAaE,EAAOF,WAExB,KAAO,CACL,MAAM9F,EAASb,EAAgB0G,GAC/B,GAAI7F,EAAQ,CACV8F,EAAajB,GAAe7E,GAC5B,MACM+F,EAAM7G,EADM2G,EAAOxG,UAAYwG,EAAOxG,UAAU,GAAK,IAEvD0G,IACFpE,EAAOoE,EAAIpE,KAEf,CACF,CACA,OAAKmE,GAAenE,EAIbmE,EAAW3C,MAAMA,EAAOxB,EAAMlI,EAA9BqM,GAA0C7B,OAH/CxK,OAAS,EAAQ,KACViM,GAGX,CAEA,MAAMS,GAAQ,CAAC,EAEf,SAASC,KACT,CACA,MAAMC,GAA+B9P,OAAOkE,OAAO,MAC7C6L,GAA8B/P,OAAOkE,OAAO,MAC5C8L,GAA8BhQ,OAAOkE,OAAO,MAC5C+L,GAA6BjQ,OAAOkE,OAAO,MACjD,SAASgM,GAAelP,EAAUU,QACF,IAA1BsO,GAAYhP,KACdgP,GAAYhP,GAA4BhB,OAAOkE,OAAO,OAExD,MAAMiM,EAAsBH,GAAYhP,GACnCmP,EAAoBzO,KACvByO,EAAoBzO,IAAU,EAC9BmK,YAAW,KACTsE,EAAoBzO,IAAU,EAjYpC,SAAyBV,EAAUU,QACA,IAA7BuK,EAAejL,KACjBiL,EAAejL,GAA4BhB,OAAOkE,OAAO,OAE3D,MAAMkM,EAAyBnE,EAAejL,GACzCoP,EAAuB1O,KAC1B0O,EAAuB1O,IAAU,EACjCmK,YAAW,KAET,GADAuE,EAAuB1O,IAAU,OACL,IAAxBsK,EAAUhL,SAAwD,IAAhCgL,EAAUhL,GAAUU,GACxD,OAEF,MAAM2K,EAAQL,EAAUhL,GAAUU,GAAQJ,MAAM,GAChD,IAAK+K,EAAMnL,OACT,OAEF,MAAMsD,EAAUE,EAAW1D,EAAUU,GACrC,IAAI2O,GAAa,EACjBhE,EAAM9I,SAASV,IACb,MAAMH,EAAQG,EAAKH,MACb4N,EAAY5N,EAAM6N,QAAQrP,OAChCwB,EAAM6N,QAAU7N,EAAM6N,QAAQjE,QAAQtK,IACpC,GAAIA,EAAKN,SAAWA,EAClB,OAAO,EAET,MAAME,EAAOI,EAAKJ,KAClB,QAA4B,IAAxB4C,EAAQ9B,MAAMd,GAChBc,EAAM8N,OAAOhN,KAAK,CAChBxC,WACAU,SACAE,aAEG,SAA8B,IAA1B4C,EAAQI,QAAQhD,GAQzB,OADAyO,GAAa,GACN,EAPP3N,EAAMkC,QAAQpB,KAAK,CACjBxC,WACAU,SACAE,QAKJ,CACA,OAAO,CAAK,IAEVc,EAAM6N,QAAQrP,SAAWoP,IACtBD,GACHnE,EAAe,CACb,CACElL,WACAU,WAEDmB,EAAKwF,IAEVxF,EAAKK,SAASR,EAAM8N,OAAOlP,MAAM,GAAIoB,EAAMkC,QAAQtD,MAAM,GAAIoB,EAAM6N,QAAQjP,MAAM,GAAIuB,EAAK6K,OAC5F,GACA,IAGR,CAwUM+C,CAAgBzP,EAAUU,EAAO,IAGvC,CACA,MAAMgP,GAA8B1Q,OAAOkE,OAAO,MAClD,SAASyM,GAAa3P,EAAUU,EAAQgB,QASR,IAA1BqN,GAAY/O,KACd+O,GAAY/O,GAA4BhB,OAAOkE,OAAO,OAExD,MAAM0M,EAAsBb,GAAY/O,QACX,IAAzBiP,GAAWjP,KACbiP,GAAWjP,GAA4BhB,OAAOkE,OAAO,OAEvD,MAAM2M,EAAqBZ,GAAWjP,QACP,IAA3B8O,GAAa9O,KACf8O,GAAa9O,GAA4BhB,OAAOkE,OAAO,OAEzD,MAAM4M,EAAuBhB,GAAa9O,QACN,IAAhC4P,EAAoBlP,GACtBkP,EAAoBlP,GAAUgB,EAE9BkO,EAAoBlP,GAAUkP,EAAoBlP,GAAQiI,OAAOjH,GAAOqO,OAErEF,EAAmBnP,KACtBmP,EAAmBnP,IAAU,EAC7BmK,YAAW,KACTgF,EAAmBnP,IAAU,EAC7B,MAAMsP,EAASJ,EAAoBlP,UAC5BkP,EAAoBlP,GAC3B,MAAM8N,EAAM7G,EAAa3H,GACzB,IAAKwO,EAEH,YAlCN,WACE,MAAMpN,GAAoB,KAAbpB,EAAkB,GAAK,IAAMA,EAAW,KAAOU,EACtDuP,EAAO1K,KAAKmB,MAAMzC,KAAKC,MAAQ,KACjCwL,GAAYtO,GAAO6O,IACrBP,GAAYtO,GAAO6O,EACnBC,QAAQnC,MAAM,iCAAmC3M,EAAM,6CAE3D,CA0BMqC,GAGa+K,EAAI9E,QAAQ1J,EAAUU,EAAQsP,GACtCzN,SAASV,IACdwM,GAAarO,EAAU6B,GAAM,CAACnC,EAAMqO,KAClC,MAAMvK,EAAUE,EAAW1D,EAAUU,GACrC,GAAoB,kBAAThB,EAAmB,CAC5B,GAAc,MAAVqO,EACF,OAEF,MAAM/J,EAAIC,KAAKC,MACfrC,EAAKH,MAAMa,SAAS3B,IAClB4C,EAAQI,QAAQhD,GAAQoD,CAAC,GAE7B,MACE,IACE,MAAMmM,EAASrM,EAAWN,EAAS9D,GACnC,IAAKyQ,EAAOjQ,OACV,OAEF,MAAMqP,EAAUO,EAAqBpP,GACrCyP,EAAO5N,SAAS3B,WACP2O,EAAQ3O,EAAK,IAElBgO,GAAMwB,OACRxB,GAAMwB,MAAMpQ,EAAUN,EAI1B,CAFE,MAAO2Q,GACPH,QAAQnC,MAAMsC,EAChB,CAEFnB,GAAelP,EAAUU,EAAO,GAChC,GACF,IAGR,CACA,MAAM4P,GAAYA,CAAC5O,EAAOQ,KACxB,MAAMqO,EAzXR,SAAqBvE,GAA4C,IAAtClM,IAAQG,UAAAC,OAAA,QAAAC,IAAAF,UAAA,KAAAA,UAAA,GAASmE,EAAWnE,UAAAC,OAAA,QAAAC,IAAAF,UAAA,IAAAA,UAAA,GACrD,MAAMU,EAAS,GAWf,OAVAqL,EAAKzJ,SAASV,IACZ,MAAMb,EAAuB,kBAATa,EAAoBjC,EAAaiC,GAAM,EAAOuC,GAAevC,EAC5E/B,IAAYe,EAAaG,EAAMoD,IAClCzD,EAAO6B,KAAK,CACVxC,SAAUgB,EAAKhB,SACfU,OAAQM,EAAKN,OACbE,KAAMI,EAAKJ,MAEf,IAEKD,CACT,CA4WuB6P,CAAY9O,GAAO,EAAM2C,KACxCoM,EAxhBR,SAAmB/O,GACjB,MAAMf,EAAS,CACb6O,OAAQ,GACR5L,QAAS,GACT2L,QAAS,IAEL/L,EAA0BxE,OAAOkE,OAAO,MAC9CxB,EAAMqO,MAAK,CAACW,EAAGC,IACTD,EAAE1Q,WAAa2Q,EAAE3Q,SACZ0Q,EAAE1Q,SAAS4Q,cAAcD,EAAE3Q,UAEhC0Q,EAAEhQ,SAAWiQ,EAAEjQ,OACVgQ,EAAEhQ,OAAOkQ,cAAcD,EAAEjQ,QAE3BgQ,EAAE9P,KAAKgQ,cAAcD,EAAE/P,QAEhC,IAAIiQ,EAAW,CACb7Q,SAAU,GACVU,OAAQ,GACRE,KAAM,IAiCR,OA/BAc,EAAMa,SAASvB,IACb,GAAI6P,EAASjQ,OAASI,EAAKJ,MAAQiQ,EAASnQ,SAAWM,EAAKN,QAAUmQ,EAAS7Q,WAAagB,EAAKhB,SAC/F,OAEF6Q,EAAW7P,EACX,MAAMhB,EAAWgB,EAAKhB,SAChBU,EAASM,EAAKN,OACdE,EAAOI,EAAKJ,UACQ,IAAtB4C,EAAQxD,KACVwD,EAAQxD,GAA4BhB,OAAOkE,OAAO,OAEpD,MAAMS,EAAkBH,EAAQxD,QACA,IAA5B2D,EAAgBjD,KAClBiD,EAAgBjD,GAAUgD,EAAW1D,EAAUU,IAEjD,MAAMoQ,EAAenN,EAAgBjD,GACrC,IAAIsL,EAEFA,OAD+B,IAA7B8E,EAAapP,MAAMd,GACdD,EAAO6O,OACM,KAAX9O,QAAgD,IAA/BoQ,EAAalN,QAAQhD,GACxCD,EAAOiD,QAEPjD,EAAO4O,QAEhB,MAAM1N,EAAO,CACX7B,WACAU,SACAE,QAEFoL,EAAKxJ,KAAKX,EAAK,IAEVlB,CACT,CAmesBoQ,CAAUR,GAC9B,IAAKE,EAAYlB,QAAQrP,OAAQ,CAC/B,IAAI8Q,GAAe,EAQnB,OAPI9O,GACF2I,YAAW,KACLmG,GACF9O,EAASuO,EAAYjB,OAAQiB,EAAY7M,QAAS6M,EAAYlB,QAASV,GACzE,IAGG,KACLmC,GAAe,CAAK,CAExB,CACA,MAAMC,EAA2BjS,OAAOkE,OAAO,MACzCiI,EAAU,GAChB,IAAI+F,EAAcC,EAClBV,EAAYlB,QAAQhN,SAASvB,IAC3B,MAAMhB,EAAWgB,EAAKhB,SAChBU,EAASM,EAAKN,OACpB,GAAIA,IAAWyQ,GAAcnR,IAAakR,EACxC,OAEFA,EAAelR,EACfmR,EAAazQ,EACbyK,EAAQ3I,KAAK,CACXxC,WACAU,gBAE6B,IAA3BoO,GAAa9O,KACf8O,GAAa9O,GAA4BhB,OAAOkE,OAAO,OAEzD,MAAM4M,EAAuBhB,GAAa9O,QACL,IAAjC8P,EAAqBpP,KACvBoP,EAAqBpP,GAA0B1B,OAAOkE,OAAO,YAEpC,IAAvB+N,EAASjR,KACXiR,EAASjR,GAA4BhB,OAAOkE,OAAO,OAErD,MAAMkO,EAAmBH,EAASjR,QACD,IAA7BoR,EAAiB1Q,KACnB0Q,EAAiB1Q,GAAU,GAC7B,IAEF,MAAMuP,EAAOhM,KAAKC,MAkBlB,OAjBAuM,EAAYlB,QAAQhN,SAASvB,IAC3B,MAAMhB,EAAWgB,EAAKhB,SAChBU,EAASM,EAAKN,OACdE,EAAOI,EAAKJ,KACZyQ,EAAevC,GAAa9O,GAAUU,QACjB,IAAvB2Q,EAAazQ,KACfyQ,EAAazQ,GAAQqP,EACrBgB,EAASjR,GAAUU,GAAQ8B,KAAK5B,GAClC,IAEFuK,EAAQ5I,SAASsF,IACf,MAAM7H,EAAW6H,EAAO7H,SAClBU,EAASmH,EAAOnH,OAClBuQ,EAASjR,GAAUU,GAAQR,QAC7ByP,GAAa3P,EAAUU,EAAQuQ,EAASjR,GAAUU,GACpD,IAEKwB,EAndT,SAAuBA,EAAUR,EAAO4P,GACtC,MAAMjK,EAAKmE,IACLkB,EAAQxB,EAAeqG,KAAK,KAAMD,EAAgBjK,GACxD,IAAK3F,EAAM6N,QAAQrP,OACjB,OAAOwM,EAET,MAAM7K,EAAO,CACXwF,KACA3F,QACAQ,WACAwK,SAcF,OAZA4E,EAAe/O,SAASsF,IACtB,MAAM7H,EAAW6H,EAAO7H,SAClBU,EAASmH,EAAOnH,YACM,IAAxBsK,EAAUhL,KACZgL,EAAUhL,GAA4BhB,OAAOkE,OAAO,OAEtD,MAAMkI,EAAoBJ,EAAUhL,QACF,IAA9BoL,EAAkB1K,KACpB0K,EAAkB1K,GAAU,IAE9B0K,EAAkB1K,GAAQ8B,KAAKX,EAAK,IAE/B6K,CACT,CA0boB8E,CAActP,EAAUuO,EAAatF,GAAW0D,EAAa,EAmB3E4C,GAAe,WACfC,GAAc,UACdC,GAAWD,GAAc,SACzBE,GAAaF,GAAc,WAC3BG,GAAO,KAEPpJ,GAAS,CACbqJ,OAAO,EACPC,SAAS,GAEX,IAAIvC,IAAS,EACb,MAAMwC,GAAQ,CACZF,MAAO,EACPC,QAAS,GAELE,GAAY,CAChBH,MAAO,GACPC,QAAS,IAEX,IAAIG,GAA4B,qBAAX9O,OAAyB,CAAC,EAAIA,OACnD,SAAS+O,GAAU/Q,GACjB,MAAM4D,EAAO5D,EAAM,UACnB,IACE,GAAI8Q,IAAWA,GAAQlN,IAAyC,kBAAzBkN,GAAQlN,GAAM9E,OACnD,OAAOgS,GAAQlN,EAGnB,CADE,MAAOvB,IACT,CAEA,OADAgF,GAAOrH,IAAO,EACP,IACT,CACA,SAASgR,GAAS5O,EAASpC,EAAKvB,GAC9B,IAGE,OAFA2D,EAAQ6O,QAAQV,GAAU9R,EAAM4G,YAChCuL,GAAM5Q,GAAOvB,GACN,CAGT,CAFE,MAAO4D,IACP,OAAO,CACT,CACF,CACA,SAAS6O,GAAS9O,GAChB,MAAM+O,EAAS/O,EAAQgP,QAAQb,IAC/B,GAAIY,EAAQ,CACV,MAAME,EAAQC,SAASH,GACvB,OAAOE,GAAgB,CACzB,CACA,OAAO,CACT,CAiBA,MAAME,GAAYA,KAChB,GAAInD,GACF,OAEFA,IAAS,EACT,MAAMoD,EAAUrN,KAAKmB,MAAMzC,KAAKC,MAAQ2N,IAhElB,IAiEtB,SAASgB,EAAKzR,GACZ,MAAM0R,EAAOX,GAAU/Q,GACvB,IAAK0R,EACH,OAEF,MAAMN,EAAWtK,IACf,MAAMtH,EAAO8Q,GAAcxJ,EAAMzB,WAC3B5E,EAAOiR,EAAKN,QAAQ5R,GAC1B,GAAoB,kBAATiB,EACT,OAAO,EAET,IAAIkR,GAAQ,EACZ,IACE,MAAMrT,EAAOsT,KAAKC,MAAMpR,GACxB,GAAoB,kBAATnC,GAA4C,kBAAhBA,EAAK+O,QAAuB/O,EAAK+O,OAASmE,GAAoC,kBAAlBlT,EAAKM,UAA8C,kBAAdN,EAAKA,MAAiD,kBAArBA,EAAKA,KAAKgB,OACjLqS,GAAQ,MACH,CACL,MAAM/S,EAAWN,EAAKM,SAChBU,EAAShB,EAAKA,KAAKgB,OAEzBqS,EAAQjP,EADQJ,EAAW1D,EAAUU,GACThB,EAAKA,MAAMQ,OAAS,CAClD,CAGF,CAFE,MAAOuD,IACPsP,GAAQ,CACV,CAIA,OAHKA,GACHD,EAAKI,WAAWtS,GAEXmS,CAAK,EAEd,IACE,MAAMxP,EAAUuP,EAAKN,QAAQZ,IAC7B,GAAIrO,IAAYkO,GAKd,OAJIlO,GAhDZ,SAAsBC,GACpB,IACE,MAAMiP,EAAQH,GAAS9O,GACvB,IAAK,IAAI2P,EAAI,EAAGA,EAAIV,EAAOU,IACzB3P,EAAQ0P,WAAWxB,GAAcyB,EAAE1M,WAGvC,CADE,MAAOhD,IACT,CACF,CAyCU2P,CAAaN,QAxDvB,SAAmBtP,EAASpC,GAC1B,IACEoC,EAAQ6O,QAAQT,GAAYH,GAE9B,CADE,MAAOhO,IACT,CACA2O,GAAS5O,EAASpC,EAAK,EACzB,CAoDQiS,CAAUP,EAAM1R,GAGlB,IAAIqR,EAAQH,GAASQ,GACrB,IAAK,IAAIK,EAAIV,EAAQ,EAAGU,GAAK,EAAGA,IACzBX,EAAQW,KACPA,IAAMV,EAAQ,EAChBA,IAEAR,GAAU7Q,GAAKoB,KAAK2Q,IAI1Bf,GAASU,EAAM1R,EAAKqR,EAEtB,CADE,MAAOhP,IACT,CACF,CACA,IAAK,MAAMrC,KAAOqH,GAChBoK,EAAKzR,EACP,EAEIkS,GAAaA,CAACtT,EAAUN,KAI5B,SAAS0Q,EAAMhP,GACb,IAAKqH,GAAOrH,GACV,OAAO,EAET,MAAM0R,EAAOX,GAAU/Q,GACvB,IAAK0R,EACH,OAAO,EAET,IAAI5K,EAAQ+J,GAAU7Q,GAAKb,QAC3B,QAAc,IAAV2H,IACFA,EAAQ8J,GAAM5Q,IACTgR,GAASU,EAAM1R,EAAK8G,EAAQ,IAC/B,OAAO,EAGX,IACE,MAAMrG,EAAO,CACX4M,OAAQlJ,KAAKmB,MAAMzC,KAAKC,MAAQ2N,IAChC7R,WACAN,QAEFoT,EAAKT,QAAQX,GAAcxJ,EAAMzB,WAAYuM,KAAKO,UAAU1R,GAG9D,CAFE,MAAO4B,IACP,OAAO,CACT,CACA,OAAO,CACT,CA7BK+L,IACHmD,KA6BG3T,OAAOyD,KAAK/C,EAAKgC,OAAOxB,SAGzBR,EAAK2C,kBACP3C,EAAOV,OAAO2C,OAAO,CAAC,EAAGjC,IACb2C,UAET+N,EAAM,UACTA,EAAM,WACR,EAiBF,MAAMoD,GAAY,SAClB,SAASC,GAAeC,EAAQC,GAC9BA,EAAKtT,MAAMmT,IAAWjR,SAASqR,IAE7B,OADcA,EAAIC,QAEhB,IAAK,aACHH,EAAOlU,OAAQ,EACf,MACF,IAAK,WACHkU,EAAOnU,OAAQ,EAEnB,GAEJ,CACA,SAASuU,GAAoBJ,EAAQK,GACnCA,EAAM1T,MAAMmT,IAAWjR,SAASqR,IAC9B,MAAM/T,EAAQ+T,EAAIC,OAClB,OAAQhU,GACN,IAAK,OACL,IAAK,SACL,IAAK,QACH6T,EAAO9O,OAAS/E,EAChB,MACF,IAAK,MACL,IAAK,SACL,IAAK,SACH6T,EAAO7O,OAAShF,EAChB,MACF,IAAK,QACL,IAAK,OACH6T,EAAOpT,OAAQ,EACf,MACF,IAAK,OACHoT,EAAOpT,OAAQ,EACnB,GAEJ,CAEA,SAAS0T,GAAiBnU,GAAyB,IAAlBoU,EAAYhU,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EAC9C,MAAMiU,EAAQrU,EAAM2H,QAAQ,aAAc,IAC1C,SAASmG,EAAQwG,GACf,KAAOA,EAAS,GACdA,GAAU,EAEZ,OAAOA,EAAS,CAClB,CACA,GAAc,KAAVD,EAAc,CAChB,MAAMpO,EAAM4M,SAAS7S,GACrB,OAAOmG,MAAMF,GAAO,EAAI6H,EAAQ7H,EAClC,CAAO,GAAIoO,IAAUrU,EAAO,CAC1B,IAAIQ,EAAQ,EACZ,OAAQ6T,GACN,IAAK,IACH7T,EAAQ,GACR,MACF,IAAK,MACHA,EAAQ,GAEZ,GAAIA,EAAO,CACT,IAAIyF,EAAMC,WAAWlG,EAAMS,MAAM,EAAGT,EAAMK,OAASgU,EAAMhU,SACzD,OAAI8F,MAAMF,GACD,GAETA,GAAYzF,EACLyF,EAAM,IAAM,EAAI6H,EAAQ7H,GAAO,EACxC,CACF,CACA,OAAOmO,CACT,CAKA,MAAMG,GAAc,CAChB,MAAS,6BACT,WAAc,+BACd,eAAe,EACf,KAAQ,MACR,MAAS,CAAC,GAKRC,GAAc1U,wBAAA,GAAQ+E,GAAQ,IAAEC,QAAQ,IAgI9C,GANAN,GAAiB,GAEjBqD,EAAa,GAAI+B,GAIO,qBAAb6K,UAA8C,qBAAXlR,OAAwB,CAElEwL,GAAMwB,MAAQkD,GACdX,KACA,MAAMT,EAAU9O,OAEhB,QAA+B,IAA3B8O,EAAQqC,eAA2B,CACnC,MAAMC,EAAUtC,EAAQqC,eAClB9Q,EAAM,iCACW,kBAAZ+Q,GAAoC,OAAZA,IAC9BA,aAAmBlS,MAAQkS,EAAU,CAACA,IAAUjS,SAASV,IACtD,KAGoB,kBAATA,GACM,OAATA,GACAA,aAAgBS,OAEM,kBAAfT,EAAKH,OACW,kBAAhBG,EAAKnB,SA1gDpC,SAAuBhB,EAAMM,GAC3B,GAAoB,kBAATN,EACT,OAAO,EAKT,GAHwB,kBAAbM,IACTA,EAAoC,kBAAlBN,EAAKM,SAAwBN,EAAKM,SAAW,IAE7DoE,GAA4B,KAAbpE,IAA2C,kBAAhBN,EAAKgB,QAAuC,KAAhBhB,EAAKgB,QAAgB,CAC7F,IAAI+T,GAAQ,EASZ,OARI3R,EAAuBpD,KACzBA,EAAKgB,OAAS,GACduB,EAAavC,GAAM,CAACkB,EAAMI,KACpBA,GAAQwD,EAAQ5D,EAAMI,KACxByT,GAAQ,EACV,KAGGA,CACT,CACA,QAA2B,kBAAhB/U,EAAKgB,SAAwBG,EAAa,CACnDb,WACAU,OAAQhB,EAAKgB,OACbE,KAAM,UAKCkD,EADOJ,EAAW1D,EAAUN,EAAKgB,QACbhB,EAC/B,CAg/CyBgV,CAAc7S,KACfqO,QAAQnC,MAAMtK,EAKtB,CAFA,MAAOkR,GACHzE,QAAQnC,MAAMtK,EAClB,IAGZ,CAEA,QAAiC,IAA7ByO,EAAQ0C,iBAA6B,CACrC,MAAMC,EAAY3C,EAAQ0C,iBAC1B,GAAyB,kBAAdC,GAAwC,OAAdA,EACjC,IAAK,IAAIzT,KAAOyT,EAAW,CACvB,MAAMpR,EAAM,oBAAsBrC,EAAM,gBACxC,IACI,MAAMvB,EAAQgV,EAAUzT,GACxB,GAAqB,kBAAVvB,IACNA,QACmB,IAApBA,EAAMiI,UACN,SAECS,EAAenH,EAAKvB,IACrBqQ,QAAQnC,MAAMtK,EAKtB,CAFA,MAAOkR,IACHzE,QAAQnC,MAAMtK,EAClB,CACJ,CAER,CACJ,CACA,MAAMqR,WAAsBC,EAAMC,UAC9BC,YAAY/O,GACRgP,MAAMhP,GACNiP,KAAKC,MAAQ,CAETpU,KAAM,KAEd,CAIAqU,gBACQF,KAAKG,WACLH,KAAKG,SAAS5I,QACdyI,KAAKG,SAAW,KAExB,CAIAC,SAASvU,GACDmU,KAAKC,MAAMpU,OAASA,GACpBmU,KAAKK,SAAS,CACVxU,QAGZ,CAIAyU,WAAWC,GACP,MAAMN,EAAQD,KAAKC,MACbpU,EAAOmU,KAAKjP,MAAMlF,KAExB,GAAoB,kBAATA,GACE,OAATA,GACqB,kBAAdA,EAAKgC,KAUZ,OARAmS,KAAKQ,MAAQ,GACbR,KAAKE,sBACDK,GAA0B,OAAfN,EAAMpU,OAEjBmU,KAAKI,SAAS,CACV7V,KAAMD,EAASuB,MAM3B,IAAI4U,EACJ,GAAoB,kBAAT5U,GAC0C,QAAhD4U,EAAWhW,EAAaoB,GAAM,GAAO,IAGtC,OAFAmU,KAAKE,qBACLF,KAAKI,SAAS,MAIlB,MAAM7V,EAAO6E,EAAYqR,GACzB,GAAa,OAATlW,GAeJ,GAAIyV,KAAKQ,QAAU3U,GAAuB,OAAfoU,EAAMpU,KAAe,CAE5CmU,KAAKE,gBACLF,KAAKQ,MAAQ3U,EAEb,MAAM6U,EAAU,CAAC,WACO,KAApBD,EAASlV,QACTmV,EAAQrT,KAAK,YAAcoT,EAASlV,QAEd,KAAtBkV,EAAS5V,UACT6V,EAAQrT,KAAK,YAAcoT,EAAS5V,UAGxCmV,KAAKI,SAAS,CACV7V,OACAmW,YAEAV,KAAKjP,MAAM4P,QACXX,KAAKjP,MAAM4P,OAAO9U,EAE1B,OAjCSmU,KAAKG,UAAYH,KAAKG,SAAS1U,OAASI,IAEzCmU,KAAKE,gBACLF,KAAKQ,MAAQ,GACbR,KAAKI,SAAS,MACdJ,KAAKG,SAAW,CACZ1U,KAAMI,EACN0L,MAAO4D,GAAU,CAACsF,GAAWT,KAAKM,WAAWlE,KAAK4D,MAAM,KA2BxE,CAIAY,oBACIZ,KAAKM,YAAW,EACpB,CAIAO,mBAAmBC,GACXA,EAASjV,OAASmU,KAAKjP,MAAMlF,MAC7BmU,KAAKM,YAAW,EAExB,CAIAS,uBACIf,KAAKE,eACT,CAIAc,SACI,MAAMjQ,EAAQiP,KAAKjP,MACblF,EAAOmU,KAAKC,MAAMpU,KACxB,GAAa,OAATA,EAEA,OAAOkF,EAAMkQ,SACPlQ,EAAMkQ,SACNrB,EAAMsB,cAAc,OAAQ,CAAC,GAGvC,IAAIC,EAAWpQ,EAUf,OATIlF,EAAK6U,UACLS,EAAQ3W,wBAAA,GACDuG,GAAK,IACRqQ,WAAuC,kBAApBrQ,EAAMqQ,UACnBrQ,EAAMqQ,UAAY,IAClB,IAAMvV,EAAK6U,QAAQ9U,KAAK,QAzT/BoV,EAEfnV,EAEAkF,EAEAvB,EAEA6R,KAEI,MAAMC,EAAe9R,EAAS0P,GAAiB3P,EAEzC0B,EAAiBtB,EAAoB2R,EAAcvQ,GAEnDwQ,EAA+B,kBAAhBxQ,EAAMwQ,OAAsC,OAAhBxQ,EAAMwQ,MACjDxQ,EAAMwQ,MACN,CAAC,EAEDC,EAAchX,wBAAA,GAAQyU,IAAW,IAAEoC,MAAKE,UAE9C,IAAK,IAAItV,KAAO8E,EAAO,CACnB,MAAMrG,EAAQqG,EAAM9E,GACpB,QAAc,IAAVvB,EAGJ,OAAQuB,GAEJ,IAAK,OACL,IAAK,QACL,IAAK,WACL,IAAK,SACL,IAAK,OACL,IAAK,UACD,MAEJ,IAAK,SACL,IAAK,QACL,IAAK,QACDgF,EAAehF,IACD,IAAVvB,GAA4B,SAAVA,GAA8B,IAAVA,EAC1C,MAEJ,IAAK,OACoB,kBAAVA,GACP4T,GAAerN,EAAgBvG,GAEnC,MAEJ,IAAK,QACoB,kBAAVA,GACPiU,GAAoB1N,EAAgBvG,GAExC,MAEJ,IAAK,QACD6W,EAAME,MAAQ/W,EACd,MAEJ,IAAK,SACoB,kBAAVA,EACPuG,EAAehF,GAAO4S,GAAiBnU,GAEjB,kBAAVA,IACZuG,EAAehF,GAAOvB,GAE1B,MAEJ,IAAK,aACL,IAAK,eACa,IAAVA,GAA4B,SAAVA,UACX8W,EAAe,eAE1B,MAEJ,aAC8B,IAAtBF,EAAarV,KACbuV,EAAevV,GAAOvB,GAGtC,CAEA,MAAMgC,EAAOsE,EAAUnF,EAAMoF,GAE7B,IAAIyQ,EAAe,EACfxP,EAAKnB,EAAMmB,GACG,kBAAPA,IAEPA,EAAKA,EAAGG,QAAQ,KAAM,MAG1BmP,EAAeG,wBAA0B,CACrCC,OAAQ7P,EAAWrF,EAAKmB,KAAMqE,EAAK,IAAMA,EAAK,KAAOwP,IAAiB,iBAE1E,IAAK,IAAIzV,KAAOS,EAAK+E,WACjB+P,EAAevV,GAAOS,EAAK+E,WAAWxF,GAK1C,OAHIS,EAAK8C,aAAkC,IAAxB+R,EAAMM,gBACrBN,EAAMM,cAAgB,YAEnBjC,EAAMsB,cAAc,MAAOM,EAAe,EA0NtCR,CAAOnV,EAAKtB,KAAM4W,EAAUpQ,EAAM+Q,QAAS/Q,EAAMgR,KAC5D,EAOJ,MAAMC,GAAOpC,EAAMqC,YAAW,SAAclR,EAAOsQ,GAC/C,MAAMF,EAAQ3W,wBAAA,GACPuG,GAAK,IACRgR,KAAMV,EACNS,SAAS,IAEb,OAAOlC,EAAMsB,cAAcvB,GAAewB,EAC9C,IAMmBvB,EAAMqC,YAAW,SAAoBlR,EAAOsQ,GAC3D,MAAMF,EAAQ3W,wBAAA,GAAQuG,GAAK,IAAEgR,KAAMV,EAAKS,SAAS,IACjD,OAAOlC,EAAMsB,cAAcvB,GAAewB,EAC9C,G,mCCzhEA,kFAEA,MAAMe,EAAY,CAAC,YAAa,YAAa,UAAW,UAAW,YAgBnE,SAASC,EAAalB,EAAU5C,GAC9B,MAAM+D,EAAgBxC,WAAeyC,QAAQpB,GAAU9K,OAAOmM,SAC9D,OAAOF,EAAcG,QAAO,CAACC,EAAQC,EAAO1P,KAC1CyP,EAAOnV,KAAKoV,GACR1P,EAAQqP,EAAcrX,OAAS,GACjCyX,EAAOnV,KAAmBuS,eAAmBvB,EAAW,CACtDpS,IAAK,aAAFuH,OAAeT,MAGfyP,IACN,GACL,CACA,MA+DME,EAAYC,YAAO,MAAO,CAC9BlX,KAAM,WACNmX,KAAM,OACNC,kBAAmBA,CAAC9R,EAAO+R,IAClB,CAACA,EAAOC,OAJDJ,EAvDGZ,IAGf,IAHgB,WACpBiB,EAAU,MACVC,GACDlB,EACKe,EAASI,YAAS,CACpBC,QAAS,OACTC,cAAe,UACdC,YAAkB,CACnBJ,SACCK,YAAwB,CACzBC,OAAQP,EAAWQ,UACnBC,YAAaR,EAAMQ,YAAYF,UAC7BG,IAAa,CACfN,cAAeM,OAEjB,GAAIV,EAAWW,QAAS,CACtB,MAAMC,EAAcC,YAAmBZ,GACjCvP,EAAO7J,OAAOyD,KAAK2V,EAAMQ,YAAYF,QAAQhB,QAAO,CAACuB,EAAKC,MAC5B,kBAAvBf,EAAWW,SAA0D,MAAlCX,EAAWW,QAAQI,IAAuD,kBAAzBf,EAAWQ,WAA8D,MAApCR,EAAWQ,UAAUO,MACvJD,EAAIC,IAAc,GAEbD,IACN,CAAC,GACEE,EAAkBV,YAAwB,CAC9CC,OAAQP,EAAWQ,UACnB9P,SAEIuQ,EAAgBX,YAAwB,CAC5CC,OAAQP,EAAWW,QACnBjQ,SAE6B,kBAApBsQ,GACTna,OAAOyD,KAAK0W,GAAiB5W,SAAQ,CAAC2W,EAAYhR,EAAO0Q,KAEvD,IADuBO,EAAgBD,GAClB,CACnB,MAAMG,EAAyBnR,EAAQ,EAAIiR,EAAgBP,EAAY1Q,EAAQ,IAAM,SACrFiR,EAAgBD,GAAcG,CAChC,KAGJ,MAAMC,EAAqBA,CAACT,EAAWK,KACrC,MAAO,CACL,gCAAiC,CAC/BK,OAAQ,EACR,CAAC,SAAD5Q,QApDmBgQ,EAoDYO,EAAaC,EAAgBD,GAAcf,EAAWQ,UAnDtF,CACLpN,IAAK,OACL,cAAe,QACfiO,OAAQ,MACR,iBAAkB,UAClBb,MA8C0Gc,YAASV,EAAaF,KApDvGF,KAsDtB,EAEHV,EAASyB,YAAUzB,EAAQO,YAAkB,CAC3CJ,SACCgB,EAAeE,GACpB,CAEA,OADArB,EAAS0B,YAAwBvB,EAAMQ,YAAaX,GAC7CA,CAAM,IAST2B,EAAqB7E,cAAiB,SAAe8E,EAASrD,GAClE,MAAMsD,EAAaC,YAAc,CAC/B7T,MAAO2T,EACPjZ,KAAM,aAEFsF,EAAQ8T,YAAaF,IACrB,UACFG,EAAY,MAAK,UACjBtB,EAAY,SAAQ,QACpBG,EAAU,EAAC,QACXoB,EAAO,SACP9D,GACElQ,EACJiU,EAAQC,YAA8BlU,EAAOmR,GACzCc,EAAa,CACjBQ,YACAG,WAEF,OAAoBuB,cAAKxC,EAAWQ,YAAS,CAC3CiC,GAAIL,EACJ9B,WAAYA,EACZ3B,IAAKA,GACJ2D,EAAO,CACR/D,SAAU8D,EAAU5C,EAAalB,EAAU8D,GAAW9D,IAE1D,IAmCewD,K", "file": "static/js/5.d31392a9.chunk.js", "sourcesContent": ["import React from 'react';\n\nconst matchName = /^[a-z0-9]+(-[a-z0-9]+)*$/;\nconst iconDefaults = Object.freeze({\n  left: 0,\n  top: 0,\n  width: 16,\n  height: 16,\n  rotate: 0,\n  vFlip: false,\n  hFlip: false\n});\nfunction fullIcon(data) {\n  return { ...iconDefaults, ...data };\n}\n\nconst stringToIcon = (value, validate, allowSimpleName, provider = \"\") => {\n  const colonSeparated = value.split(\":\");\n  if (value.slice(0, 1) === \"@\") {\n    if (colonSeparated.length < 2 || colonSeparated.length > 3) {\n      return null;\n    }\n    provider = colonSeparated.shift().slice(1);\n  }\n  if (colonSeparated.length > 3 || !colonSeparated.length) {\n    return null;\n  }\n  if (colonSeparated.length > 1) {\n    const name2 = colonSeparated.pop();\n    const prefix = colonSeparated.pop();\n    const result = {\n      provider: colonSeparated.length > 0 ? colonSeparated[0] : provider,\n      prefix,\n      name: name2\n    };\n    return validate && !validateIcon(result) ? null : result;\n  }\n  const name = colonSeparated[0];\n  const dashSeparated = name.split(\"-\");\n  if (dashSeparated.length > 1) {\n    const result = {\n      provider,\n      prefix: dashSeparated.shift(),\n      name: dashSeparated.join(\"-\")\n    };\n    return validate && !validateIcon(result) ? null : result;\n  }\n  if (allowSimpleName && provider === \"\") {\n    const result = {\n      provider,\n      prefix: \"\",\n      name\n    };\n    return validate && !validateIcon(result, allowSimpleName) ? null : result;\n  }\n  return null;\n};\nconst validateIcon = (icon, allowSimpleName) => {\n  if (!icon) {\n    return false;\n  }\n  return !!((icon.provider === \"\" || icon.provider.match(matchName)) && (allowSimpleName && icon.prefix === \"\" || icon.prefix.match(matchName)) && icon.name.match(matchName));\n};\n\nfunction mergeIconData(icon, alias) {\n  const result = { ...icon };\n  for (const key in iconDefaults) {\n    const prop = key;\n    if (alias[prop] !== void 0) {\n      const value = alias[prop];\n      if (result[prop] === void 0) {\n        result[prop] = value;\n        continue;\n      }\n      switch (prop) {\n        case \"rotate\":\n          result[prop] = (result[prop] + value) % 4;\n          break;\n        case \"hFlip\":\n        case \"vFlip\":\n          result[prop] = value !== result[prop];\n          break;\n        default:\n          result[prop] = value;\n      }\n    }\n  }\n  return result;\n}\n\nfunction getIconData$1(data, name, full = false) {\n  function getIcon(name2, iteration) {\n    if (data.icons[name2] !== void 0) {\n      return Object.assign({}, data.icons[name2]);\n    }\n    if (iteration > 5) {\n      return null;\n    }\n    const aliases = data.aliases;\n    if (aliases && aliases[name2] !== void 0) {\n      const item = aliases[name2];\n      const result2 = getIcon(item.parent, iteration + 1);\n      if (result2) {\n        return mergeIconData(result2, item);\n      }\n      return result2;\n    }\n    const chars = data.chars;\n    if (!iteration && chars && chars[name2] !== void 0) {\n      return getIcon(chars[name2], iteration + 1);\n    }\n    return null;\n  }\n  const result = getIcon(name, 0);\n  if (result) {\n    for (const key in iconDefaults) {\n      if (result[key] === void 0 && data[key] !== void 0) {\n        result[key] = data[key];\n      }\n    }\n  }\n  return result && full ? fullIcon(result) : result;\n}\n\nfunction isVariation(item) {\n  for (const key in iconDefaults) {\n    if (item[key] !== void 0) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction parseIconSet(data, callback, options) {\n  options = options || {};\n  const names = [];\n  if (typeof data !== \"object\" || typeof data.icons !== \"object\") {\n    return names;\n  }\n  if (data.not_found instanceof Array) {\n    data.not_found.forEach((name) => {\n      callback(name, null);\n      names.push(name);\n    });\n  }\n  const icons = data.icons;\n  Object.keys(icons).forEach((name) => {\n    const iconData = getIconData$1(data, name, true);\n    if (iconData) {\n      callback(name, iconData);\n      names.push(name);\n    }\n  });\n  const parseAliases = options.aliases || \"all\";\n  if (parseAliases !== \"none\" && typeof data.aliases === \"object\") {\n    const aliases = data.aliases;\n    Object.keys(aliases).forEach((name) => {\n      if (parseAliases === \"variations\" && isVariation(aliases[name])) {\n        return;\n      }\n      const iconData = getIconData$1(data, name, true);\n      if (iconData) {\n        callback(name, iconData);\n        names.push(name);\n      }\n    });\n  }\n  return names;\n}\n\nconst optionalProperties = {\n  provider: \"string\",\n  aliases: \"object\",\n  not_found: \"object\"\n};\nfor (const prop in iconDefaults) {\n  optionalProperties[prop] = typeof iconDefaults[prop];\n}\nfunction quicklyValidateIconSet(obj) {\n  if (typeof obj !== \"object\" || obj === null) {\n    return null;\n  }\n  const data = obj;\n  if (typeof data.prefix !== \"string\" || !obj.icons || typeof obj.icons !== \"object\") {\n    return null;\n  }\n  for (const prop in optionalProperties) {\n    if (obj[prop] !== void 0 && typeof obj[prop] !== optionalProperties[prop]) {\n      return null;\n    }\n  }\n  const icons = data.icons;\n  for (const name in icons) {\n    const icon = icons[name];\n    if (!name.match(matchName) || typeof icon.body !== \"string\") {\n      return null;\n    }\n    for (const prop in iconDefaults) {\n      if (icon[prop] !== void 0 && typeof icon[prop] !== typeof iconDefaults[prop]) {\n        return null;\n      }\n    }\n  }\n  const aliases = data.aliases;\n  if (aliases) {\n    for (const name in aliases) {\n      const icon = aliases[name];\n      const parent = icon.parent;\n      if (!name.match(matchName) || typeof parent !== \"string\" || !icons[parent] && !aliases[parent]) {\n        return null;\n      }\n      for (const prop in iconDefaults) {\n        if (icon[prop] !== void 0 && typeof icon[prop] !== typeof iconDefaults[prop]) {\n          return null;\n        }\n      }\n    }\n  }\n  return data;\n}\n\nconst storageVersion = 1;\nlet storage$1 = /* @__PURE__ */ Object.create(null);\ntry {\n  const w = window || self;\n  if (w && w._iconifyStorage.version === storageVersion) {\n    storage$1 = w._iconifyStorage.storage;\n  }\n} catch (err) {\n}\nfunction shareStorage() {\n  try {\n    const w = window || self;\n    if (w && !w._iconifyStorage) {\n      w._iconifyStorage = {\n        version: storageVersion,\n        storage: storage$1\n      };\n    }\n  } catch (err) {\n  }\n}\nfunction newStorage(provider, prefix) {\n  return {\n    provider,\n    prefix,\n    icons: /* @__PURE__ */ Object.create(null),\n    missing: /* @__PURE__ */ Object.create(null)\n  };\n}\nfunction getStorage(provider, prefix) {\n  if (storage$1[provider] === void 0) {\n    storage$1[provider] = /* @__PURE__ */ Object.create(null);\n  }\n  const providerStorage = storage$1[provider];\n  if (providerStorage[prefix] === void 0) {\n    providerStorage[prefix] = newStorage(provider, prefix);\n  }\n  return providerStorage[prefix];\n}\nfunction addIconSet(storage2, data) {\n  if (!quicklyValidateIconSet(data)) {\n    return [];\n  }\n  const t = Date.now();\n  return parseIconSet(data, (name, icon) => {\n    if (icon) {\n      storage2.icons[name] = icon;\n    } else {\n      storage2.missing[name] = t;\n    }\n  });\n}\nfunction addIconToStorage(storage2, name, icon) {\n  try {\n    if (typeof icon.body === \"string\") {\n      storage2.icons[name] = Object.freeze(fullIcon(icon));\n      return true;\n    }\n  } catch (err) {\n  }\n  return false;\n}\nfunction getIconFromStorage(storage2, name) {\n  const value = storage2.icons[name];\n  return value === void 0 ? null : value;\n}\nfunction listIcons(provider, prefix) {\n  let allIcons = [];\n  let providers;\n  if (typeof provider === \"string\") {\n    providers = [provider];\n  } else {\n    providers = Object.keys(storage$1);\n  }\n  providers.forEach((provider2) => {\n    let prefixes;\n    if (typeof provider2 === \"string\" && typeof prefix === \"string\") {\n      prefixes = [prefix];\n    } else {\n      prefixes = storage$1[provider2] === void 0 ? [] : Object.keys(storage$1[provider2]);\n    }\n    prefixes.forEach((prefix2) => {\n      const storage2 = getStorage(provider2, prefix2);\n      const icons = Object.keys(storage2.icons).map((name) => (provider2 !== \"\" ? \"@\" + provider2 + \":\" : \"\") + prefix2 + \":\" + name);\n      allIcons = allIcons.concat(icons);\n    });\n  });\n  return allIcons;\n}\n\nlet simpleNames = false;\nfunction allowSimpleNames(allow) {\n  if (typeof allow === \"boolean\") {\n    simpleNames = allow;\n  }\n  return simpleNames;\n}\nfunction getIconData(name) {\n  const icon = typeof name === \"string\" ? stringToIcon(name, true, simpleNames) : name;\n  return icon ? getIconFromStorage(getStorage(icon.provider, icon.prefix), icon.name) : null;\n}\nfunction addIcon(name, data) {\n  const icon = stringToIcon(name, true, simpleNames);\n  if (!icon) {\n    return false;\n  }\n  const storage = getStorage(icon.provider, icon.prefix);\n  return addIconToStorage(storage, icon.name, data);\n}\nfunction addCollection(data, provider) {\n  if (typeof data !== \"object\") {\n    return false;\n  }\n  if (typeof provider !== \"string\") {\n    provider = typeof data.provider === \"string\" ? data.provider : \"\";\n  }\n  if (simpleNames && provider === \"\" && (typeof data.prefix !== \"string\" || data.prefix === \"\")) {\n    let added = false;\n    if (quicklyValidateIconSet(data)) {\n      data.prefix = \"\";\n      parseIconSet(data, (name, icon) => {\n        if (icon && addIcon(name, icon)) {\n          added = true;\n        }\n      });\n    }\n    return added;\n  }\n  if (typeof data.prefix !== \"string\" || !validateIcon({\n    provider,\n    prefix: data.prefix,\n    name: \"a\"\n  })) {\n    return false;\n  }\n  const storage = getStorage(provider, data.prefix);\n  return !!addIconSet(storage, data);\n}\nfunction iconExists(name) {\n  return getIconData(name) !== null;\n}\nfunction getIcon(name) {\n  const result = getIconData(name);\n  return result ? { ...result } : null;\n}\n\nconst defaults = Object.freeze({\n  inline: false,\n  width: null,\n  height: null,\n  hAlign: \"center\",\n  vAlign: \"middle\",\n  slice: false,\n  hFlip: false,\n  vFlip: false,\n  rotate: 0\n});\nfunction mergeCustomisations(defaults2, item) {\n  const result = {};\n  for (const key in defaults2) {\n    const attr = key;\n    result[attr] = defaults2[attr];\n    if (item[attr] === void 0) {\n      continue;\n    }\n    const value = item[attr];\n    switch (attr) {\n      case \"inline\":\n      case \"slice\":\n        if (typeof value === \"boolean\") {\n          result[attr] = value;\n        }\n        break;\n      case \"hFlip\":\n      case \"vFlip\":\n        if (value === true) {\n          result[attr] = !result[attr];\n        }\n        break;\n      case \"hAlign\":\n      case \"vAlign\":\n        if (typeof value === \"string\" && value !== \"\") {\n          result[attr] = value;\n        }\n        break;\n      case \"width\":\n      case \"height\":\n        if (typeof value === \"string\" && value !== \"\" || typeof value === \"number\" && value || value === null) {\n          result[attr] = value;\n        }\n        break;\n      case \"rotate\":\n        if (typeof value === \"number\") {\n          result[attr] += value;\n        }\n        break;\n    }\n  }\n  return result;\n}\n\nconst unitsSplit = /(-?[0-9.]*[0-9]+[0-9.]*)/g;\nconst unitsTest = /^-?[0-9.]*[0-9]+[0-9.]*$/g;\nfunction calculateSize(size, ratio, precision) {\n  if (ratio === 1) {\n    return size;\n  }\n  precision = precision === void 0 ? 100 : precision;\n  if (typeof size === \"number\") {\n    return Math.ceil(size * ratio * precision) / precision;\n  }\n  if (typeof size !== \"string\") {\n    return size;\n  }\n  const oldParts = size.split(unitsSplit);\n  if (oldParts === null || !oldParts.length) {\n    return size;\n  }\n  const newParts = [];\n  let code = oldParts.shift();\n  let isNumber = unitsTest.test(code);\n  while (true) {\n    if (isNumber) {\n      const num = parseFloat(code);\n      if (isNaN(num)) {\n        newParts.push(code);\n      } else {\n        newParts.push(Math.ceil(num * ratio * precision) / precision);\n      }\n    } else {\n      newParts.push(code);\n    }\n    code = oldParts.shift();\n    if (code === void 0) {\n      return newParts.join(\"\");\n    }\n    isNumber = !isNumber;\n  }\n}\n\nfunction preserveAspectRatio(props) {\n  let result = \"\";\n  switch (props.hAlign) {\n    case \"left\":\n      result += \"xMin\";\n      break;\n    case \"right\":\n      result += \"xMax\";\n      break;\n    default:\n      result += \"xMid\";\n  }\n  switch (props.vAlign) {\n    case \"top\":\n      result += \"YMin\";\n      break;\n    case \"bottom\":\n      result += \"YMax\";\n      break;\n    default:\n      result += \"YMid\";\n  }\n  result += props.slice ? \" slice\" : \" meet\";\n  return result;\n}\nfunction iconToSVG(icon, customisations) {\n  const box = {\n    left: icon.left,\n    top: icon.top,\n    width: icon.width,\n    height: icon.height\n  };\n  let body = icon.body;\n  [icon, customisations].forEach((props) => {\n    const transformations = [];\n    const hFlip = props.hFlip;\n    const vFlip = props.vFlip;\n    let rotation = props.rotate;\n    if (hFlip) {\n      if (vFlip) {\n        rotation += 2;\n      } else {\n        transformations.push(\"translate(\" + (box.width + box.left).toString() + \" \" + (0 - box.top).toString() + \")\");\n        transformations.push(\"scale(-1 1)\");\n        box.top = box.left = 0;\n      }\n    } else if (vFlip) {\n      transformations.push(\"translate(\" + (0 - box.left).toString() + \" \" + (box.height + box.top).toString() + \")\");\n      transformations.push(\"scale(1 -1)\");\n      box.top = box.left = 0;\n    }\n    let tempValue;\n    if (rotation < 0) {\n      rotation -= Math.floor(rotation / 4) * 4;\n    }\n    rotation = rotation % 4;\n    switch (rotation) {\n      case 1:\n        tempValue = box.height / 2 + box.top;\n        transformations.unshift(\"rotate(90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\");\n        break;\n      case 2:\n        transformations.unshift(\"rotate(180 \" + (box.width / 2 + box.left).toString() + \" \" + (box.height / 2 + box.top).toString() + \")\");\n        break;\n      case 3:\n        tempValue = box.width / 2 + box.left;\n        transformations.unshift(\"rotate(-90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\");\n        break;\n    }\n    if (rotation % 2 === 1) {\n      if (box.left !== 0 || box.top !== 0) {\n        tempValue = box.left;\n        box.left = box.top;\n        box.top = tempValue;\n      }\n      if (box.width !== box.height) {\n        tempValue = box.width;\n        box.width = box.height;\n        box.height = tempValue;\n      }\n    }\n    if (transformations.length) {\n      body = '<g transform=\"' + transformations.join(\" \") + '\">' + body + \"</g>\";\n    }\n  });\n  let width, height;\n  if (customisations.width === null && customisations.height === null) {\n    height = \"1em\";\n    width = calculateSize(height, box.width / box.height);\n  } else if (customisations.width !== null && customisations.height !== null) {\n    width = customisations.width;\n    height = customisations.height;\n  } else if (customisations.height !== null) {\n    height = customisations.height;\n    width = calculateSize(height, box.width / box.height);\n  } else {\n    width = customisations.width;\n    height = calculateSize(width, box.height / box.width);\n  }\n  if (width === \"auto\") {\n    width = box.width;\n  }\n  if (height === \"auto\") {\n    height = box.height;\n  }\n  width = typeof width === \"string\" ? width : width.toString() + \"\";\n  height = typeof height === \"string\" ? height : height.toString() + \"\";\n  const result = {\n    attributes: {\n      width,\n      height,\n      preserveAspectRatio: preserveAspectRatio(customisations),\n      viewBox: box.left.toString() + \" \" + box.top.toString() + \" \" + box.width.toString() + \" \" + box.height.toString()\n    },\n    body\n  };\n  if (customisations.inline) {\n    result.inline = true;\n  }\n  return result;\n}\n\nfunction buildIcon(icon, customisations) {\n  return iconToSVG(fullIcon(icon), customisations ? mergeCustomisations(defaults, customisations) : defaults);\n}\n\nconst regex = /\\sid=\"(\\S+)\"/g;\nconst randomPrefix = \"IconifyId\" + Date.now().toString(16) + (Math.random() * 16777216 | 0).toString(16);\nlet counter = 0;\nfunction replaceIDs(body, prefix = randomPrefix) {\n  const ids = [];\n  let match;\n  while (match = regex.exec(body)) {\n    ids.push(match[1]);\n  }\n  if (!ids.length) {\n    return body;\n  }\n  ids.forEach((id) => {\n    const newID = typeof prefix === \"function\" ? prefix(id) : prefix + (counter++).toString();\n    const escapedID = id.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n    body = body.replace(new RegExp('([#;\"])(' + escapedID + ')([\")]|\\\\.[a-z])', \"g\"), \"$1\" + newID + \"$3\");\n  });\n  return body;\n}\n\nconst storage = /* @__PURE__ */ Object.create(null);\nfunction setAPIModule(provider, item) {\n  storage[provider] = item;\n}\nfunction getAPIModule(provider) {\n  return storage[provider] || storage[\"\"];\n}\n\nfunction createAPIConfig(source) {\n  let resources;\n  if (typeof source.resources === \"string\") {\n    resources = [source.resources];\n  } else {\n    resources = source.resources;\n    if (!(resources instanceof Array) || !resources.length) {\n      return null;\n    }\n  }\n  const result = {\n    resources,\n    path: source.path === void 0 ? \"/\" : source.path,\n    maxURL: source.maxURL ? source.maxURL : 500,\n    rotate: source.rotate ? source.rotate : 750,\n    timeout: source.timeout ? source.timeout : 5e3,\n    random: source.random === true,\n    index: source.index ? source.index : 0,\n    dataAfterTimeout: source.dataAfterTimeout !== false\n  };\n  return result;\n}\nconst configStorage = /* @__PURE__ */ Object.create(null);\nconst fallBackAPISources = [\n  \"https://api.simplesvg.com\",\n  \"https://api.unisvg.com\"\n];\nconst fallBackAPI = [];\nwhile (fallBackAPISources.length > 0) {\n  if (fallBackAPISources.length === 1) {\n    fallBackAPI.push(fallBackAPISources.shift());\n  } else {\n    if (Math.random() > 0.5) {\n      fallBackAPI.push(fallBackAPISources.shift());\n    } else {\n      fallBackAPI.push(fallBackAPISources.pop());\n    }\n  }\n}\nconfigStorage[\"\"] = createAPIConfig({\n  resources: [\"https://api.iconify.design\"].concat(fallBackAPI)\n});\nfunction addAPIProvider(provider, customConfig) {\n  const config = createAPIConfig(customConfig);\n  if (config === null) {\n    return false;\n  }\n  configStorage[provider] = config;\n  return true;\n}\nfunction getAPIConfig(provider) {\n  return configStorage[provider];\n}\nfunction listAPIProviders() {\n  return Object.keys(configStorage);\n}\n\nconst mergeParams = (base, params) => {\n  let result = base, hasParams = result.indexOf(\"?\") !== -1;\n  function paramToString(value) {\n    switch (typeof value) {\n      case \"boolean\":\n        return value ? \"true\" : \"false\";\n      case \"number\":\n        return encodeURIComponent(value);\n      case \"string\":\n        return encodeURIComponent(value);\n      default:\n        throw new Error(\"Invalid parameter\");\n    }\n  }\n  Object.keys(params).forEach((key) => {\n    let value;\n    try {\n      value = paramToString(params[key]);\n    } catch (err) {\n      return;\n    }\n    result += (hasParams ? \"&\" : \"?\") + encodeURIComponent(key) + \"=\" + value;\n    hasParams = true;\n  });\n  return result;\n};\n\nconst maxLengthCache = {};\nconst pathCache = {};\nconst detectFetch = () => {\n  let callback;\n  try {\n    callback = fetch;\n    if (typeof callback === \"function\") {\n      return callback;\n    }\n  } catch (err) {\n  }\n  return null;\n};\nlet fetchModule = detectFetch();\nfunction setFetch(fetch2) {\n  fetchModule = fetch2;\n}\nfunction getFetch() {\n  return fetchModule;\n}\nfunction calculateMaxLength(provider, prefix) {\n  const config = getAPIConfig(provider);\n  if (!config) {\n    return 0;\n  }\n  let result;\n  if (!config.maxURL) {\n    result = 0;\n  } else {\n    let maxHostLength = 0;\n    config.resources.forEach((item) => {\n      const host = item;\n      maxHostLength = Math.max(maxHostLength, host.length);\n    });\n    const url = mergeParams(prefix + \".json\", {\n      icons: \"\"\n    });\n    result = config.maxURL - maxHostLength - config.path.length - url.length;\n  }\n  const cacheKey = provider + \":\" + prefix;\n  pathCache[provider] = config.path;\n  maxLengthCache[cacheKey] = result;\n  return result;\n}\nfunction shouldAbort(status) {\n  return status === 404;\n}\nconst prepare = (provider, prefix, icons) => {\n  const results = [];\n  let maxLength = maxLengthCache[prefix];\n  if (maxLength === void 0) {\n    maxLength = calculateMaxLength(provider, prefix);\n  }\n  const type = \"icons\";\n  let item = {\n    type,\n    provider,\n    prefix,\n    icons: []\n  };\n  let length = 0;\n  icons.forEach((name, index) => {\n    length += name.length + 1;\n    if (length >= maxLength && index > 0) {\n      results.push(item);\n      item = {\n        type,\n        provider,\n        prefix,\n        icons: []\n      };\n      length = name.length;\n    }\n    item.icons.push(name);\n  });\n  results.push(item);\n  return results;\n};\nfunction getPath(provider) {\n  if (typeof provider === \"string\") {\n    if (pathCache[provider] === void 0) {\n      const config = getAPIConfig(provider);\n      if (!config) {\n        return \"/\";\n      }\n      pathCache[provider] = config.path;\n    }\n    return pathCache[provider];\n  }\n  return \"/\";\n}\nconst send = (host, params, callback) => {\n  if (!fetchModule) {\n    callback(\"abort\", 424);\n    return;\n  }\n  let path = getPath(params.provider);\n  switch (params.type) {\n    case \"icons\": {\n      const prefix = params.prefix;\n      const icons = params.icons;\n      const iconsList = icons.join(\",\");\n      path += mergeParams(prefix + \".json\", {\n        icons: iconsList\n      });\n      break;\n    }\n    case \"custom\": {\n      const uri = params.uri;\n      path += uri.slice(0, 1) === \"/\" ? uri.slice(1) : uri;\n      break;\n    }\n    default:\n      callback(\"abort\", 400);\n      return;\n  }\n  let defaultError = 503;\n  fetchModule(host + path).then((response) => {\n    const status = response.status;\n    if (status !== 200) {\n      setTimeout(() => {\n        callback(shouldAbort(status) ? \"abort\" : \"next\", status);\n      });\n      return;\n    }\n    defaultError = 501;\n    return response.json();\n  }).then((data) => {\n    if (typeof data !== \"object\" || data === null) {\n      setTimeout(() => {\n        callback(\"next\", defaultError);\n      });\n      return;\n    }\n    setTimeout(() => {\n      callback(\"success\", data);\n    });\n  }).catch(() => {\n    callback(\"next\", defaultError);\n  });\n};\nconst fetchAPIModule = {\n  prepare,\n  send\n};\n\nfunction sortIcons(icons) {\n  const result = {\n    loaded: [],\n    missing: [],\n    pending: []\n  };\n  const storage = /* @__PURE__ */ Object.create(null);\n  icons.sort((a, b) => {\n    if (a.provider !== b.provider) {\n      return a.provider.localeCompare(b.provider);\n    }\n    if (a.prefix !== b.prefix) {\n      return a.prefix.localeCompare(b.prefix);\n    }\n    return a.name.localeCompare(b.name);\n  });\n  let lastIcon = {\n    provider: \"\",\n    prefix: \"\",\n    name: \"\"\n  };\n  icons.forEach((icon) => {\n    if (lastIcon.name === icon.name && lastIcon.prefix === icon.prefix && lastIcon.provider === icon.provider) {\n      return;\n    }\n    lastIcon = icon;\n    const provider = icon.provider;\n    const prefix = icon.prefix;\n    const name = icon.name;\n    if (storage[provider] === void 0) {\n      storage[provider] = /* @__PURE__ */ Object.create(null);\n    }\n    const providerStorage = storage[provider];\n    if (providerStorage[prefix] === void 0) {\n      providerStorage[prefix] = getStorage(provider, prefix);\n    }\n    const localStorage = providerStorage[prefix];\n    let list;\n    if (localStorage.icons[name] !== void 0) {\n      list = result.loaded;\n    } else if (prefix === \"\" || localStorage.missing[name] !== void 0) {\n      list = result.missing;\n    } else {\n      list = result.pending;\n    }\n    const item = {\n      provider,\n      prefix,\n      name\n    };\n    list.push(item);\n  });\n  return result;\n}\n\nconst callbacks = /* @__PURE__ */ Object.create(null);\nconst pendingUpdates = /* @__PURE__ */ Object.create(null);\nfunction removeCallback(sources, id) {\n  sources.forEach((source) => {\n    const provider = source.provider;\n    if (callbacks[provider] === void 0) {\n      return;\n    }\n    const providerCallbacks = callbacks[provider];\n    const prefix = source.prefix;\n    const items = providerCallbacks[prefix];\n    if (items) {\n      providerCallbacks[prefix] = items.filter((row) => row.id !== id);\n    }\n  });\n}\nfunction updateCallbacks(provider, prefix) {\n  if (pendingUpdates[provider] === void 0) {\n    pendingUpdates[provider] = /* @__PURE__ */ Object.create(null);\n  }\n  const providerPendingUpdates = pendingUpdates[provider];\n  if (!providerPendingUpdates[prefix]) {\n    providerPendingUpdates[prefix] = true;\n    setTimeout(() => {\n      providerPendingUpdates[prefix] = false;\n      if (callbacks[provider] === void 0 || callbacks[provider][prefix] === void 0) {\n        return;\n      }\n      const items = callbacks[provider][prefix].slice(0);\n      if (!items.length) {\n        return;\n      }\n      const storage = getStorage(provider, prefix);\n      let hasPending = false;\n      items.forEach((item) => {\n        const icons = item.icons;\n        const oldLength = icons.pending.length;\n        icons.pending = icons.pending.filter((icon) => {\n          if (icon.prefix !== prefix) {\n            return true;\n          }\n          const name = icon.name;\n          if (storage.icons[name] !== void 0) {\n            icons.loaded.push({\n              provider,\n              prefix,\n              name\n            });\n          } else if (storage.missing[name] !== void 0) {\n            icons.missing.push({\n              provider,\n              prefix,\n              name\n            });\n          } else {\n            hasPending = true;\n            return true;\n          }\n          return false;\n        });\n        if (icons.pending.length !== oldLength) {\n          if (!hasPending) {\n            removeCallback([\n              {\n                provider,\n                prefix\n              }\n            ], item.id);\n          }\n          item.callback(icons.loaded.slice(0), icons.missing.slice(0), icons.pending.slice(0), item.abort);\n        }\n      });\n    });\n  }\n}\nlet idCounter = 0;\nfunction storeCallback(callback, icons, pendingSources) {\n  const id = idCounter++;\n  const abort = removeCallback.bind(null, pendingSources, id);\n  if (!icons.pending.length) {\n    return abort;\n  }\n  const item = {\n    id,\n    icons,\n    callback,\n    abort\n  };\n  pendingSources.forEach((source) => {\n    const provider = source.provider;\n    const prefix = source.prefix;\n    if (callbacks[provider] === void 0) {\n      callbacks[provider] = /* @__PURE__ */ Object.create(null);\n    }\n    const providerCallbacks = callbacks[provider];\n    if (providerCallbacks[prefix] === void 0) {\n      providerCallbacks[prefix] = [];\n    }\n    providerCallbacks[prefix].push(item);\n  });\n  return abort;\n}\n\nfunction listToIcons(list, validate = true, simpleNames = false) {\n  const result = [];\n  list.forEach((item) => {\n    const icon = typeof item === \"string\" ? stringToIcon(item, false, simpleNames) : item;\n    if (!validate || validateIcon(icon, simpleNames)) {\n      result.push({\n        provider: icon.provider,\n        prefix: icon.prefix,\n        name: icon.name\n      });\n    }\n  });\n  return result;\n}\n\n// src/config.ts\nvar defaultConfig = {\n  resources: [],\n  index: 0,\n  timeout: 2e3,\n  rotate: 750,\n  random: false,\n  dataAfterTimeout: false\n};\n\n// src/query.ts\nfunction sendQuery(config, payload, query, done) {\n  const resourcesCount = config.resources.length;\n  const startIndex = config.random ? Math.floor(Math.random() * resourcesCount) : config.index;\n  let resources;\n  if (config.random) {\n    let list = config.resources.slice(0);\n    resources = [];\n    while (list.length > 1) {\n      const nextIndex = Math.floor(Math.random() * list.length);\n      resources.push(list[nextIndex]);\n      list = list.slice(0, nextIndex).concat(list.slice(nextIndex + 1));\n    }\n    resources = resources.concat(list);\n  } else {\n    resources = config.resources.slice(startIndex).concat(config.resources.slice(0, startIndex));\n  }\n  const startTime = Date.now();\n  let status = \"pending\";\n  let queriesSent = 0;\n  let lastError;\n  let timer = null;\n  let queue = [];\n  let doneCallbacks = [];\n  if (typeof done === \"function\") {\n    doneCallbacks.push(done);\n  }\n  function resetTimer() {\n    if (timer) {\n      clearTimeout(timer);\n      timer = null;\n    }\n  }\n  function abort() {\n    if (status === \"pending\") {\n      status = \"aborted\";\n    }\n    resetTimer();\n    queue.forEach((item) => {\n      if (item.status === \"pending\") {\n        item.status = \"aborted\";\n      }\n    });\n    queue = [];\n  }\n  function subscribe(callback, overwrite) {\n    if (overwrite) {\n      doneCallbacks = [];\n    }\n    if (typeof callback === \"function\") {\n      doneCallbacks.push(callback);\n    }\n  }\n  function getQueryStatus() {\n    return {\n      startTime,\n      payload,\n      status,\n      queriesSent,\n      queriesPending: queue.length,\n      subscribe,\n      abort\n    };\n  }\n  function failQuery() {\n    status = \"failed\";\n    doneCallbacks.forEach((callback) => {\n      callback(void 0, lastError);\n    });\n  }\n  function clearQueue() {\n    queue.forEach((item) => {\n      if (item.status === \"pending\") {\n        item.status = \"aborted\";\n      }\n    });\n    queue = [];\n  }\n  function moduleResponse(item, response, data) {\n    const isError = response !== \"success\";\n    queue = queue.filter((queued) => queued !== item);\n    switch (status) {\n      case \"pending\":\n        break;\n      case \"failed\":\n        if (isError || !config.dataAfterTimeout) {\n          return;\n        }\n        break;\n      default:\n        return;\n    }\n    if (response === \"abort\") {\n      lastError = data;\n      failQuery();\n      return;\n    }\n    if (isError) {\n      lastError = data;\n      if (!queue.length) {\n        if (!resources.length) {\n          failQuery();\n        } else {\n          execNext();\n        }\n      }\n      return;\n    }\n    resetTimer();\n    clearQueue();\n    if (!config.random) {\n      const index = config.resources.indexOf(item.resource);\n      if (index !== -1 && index !== config.index) {\n        config.index = index;\n      }\n    }\n    status = \"completed\";\n    doneCallbacks.forEach((callback) => {\n      callback(data);\n    });\n  }\n  function execNext() {\n    if (status !== \"pending\") {\n      return;\n    }\n    resetTimer();\n    const resource = resources.shift();\n    if (resource === void 0) {\n      if (queue.length) {\n        timer = setTimeout(() => {\n          resetTimer();\n          if (status === \"pending\") {\n            clearQueue();\n            failQuery();\n          }\n        }, config.timeout);\n        return;\n      }\n      failQuery();\n      return;\n    }\n    const item = {\n      status: \"pending\",\n      resource,\n      callback: (status2, data) => {\n        moduleResponse(item, status2, data);\n      }\n    };\n    queue.push(item);\n    queriesSent++;\n    timer = setTimeout(execNext, config.rotate);\n    query(resource, payload, item.callback);\n  }\n  setTimeout(execNext);\n  return getQueryStatus;\n}\n\n// src/index.ts\nfunction setConfig(config) {\n  if (typeof config !== \"object\" || typeof config.resources !== \"object\" || !(config.resources instanceof Array) || !config.resources.length) {\n    throw new Error(\"Invalid Reduncancy configuration\");\n  }\n  const newConfig = /* @__PURE__ */ Object.create(null);\n  let key;\n  for (key in defaultConfig) {\n    if (config[key] !== void 0) {\n      newConfig[key] = config[key];\n    } else {\n      newConfig[key] = defaultConfig[key];\n    }\n  }\n  return newConfig;\n}\nfunction initRedundancy(cfg) {\n  const config = setConfig(cfg);\n  let queries = [];\n  function cleanup() {\n    queries = queries.filter((item) => item().status === \"pending\");\n  }\n  function query(payload, queryCallback, doneCallback) {\n    const query2 = sendQuery(config, payload, queryCallback, (data, error) => {\n      cleanup();\n      if (doneCallback) {\n        doneCallback(data, error);\n      }\n    });\n    queries.push(query2);\n    return query2;\n  }\n  function find(callback) {\n    const result = queries.find((value) => {\n      return callback(value);\n    });\n    return result !== void 0 ? result : null;\n  }\n  const instance = {\n    query,\n    find,\n    setIndex: (index) => {\n      config.index = index;\n    },\n    getIndex: () => config.index,\n    cleanup\n  };\n  return instance;\n}\n\nfunction emptyCallback$1() {\n}\nconst redundancyCache = /* @__PURE__ */ Object.create(null);\nfunction getRedundancyCache(provider) {\n  if (redundancyCache[provider] === void 0) {\n    const config = getAPIConfig(provider);\n    if (!config) {\n      return;\n    }\n    const redundancy = initRedundancy(config);\n    const cachedReundancy = {\n      config,\n      redundancy\n    };\n    redundancyCache[provider] = cachedReundancy;\n  }\n  return redundancyCache[provider];\n}\nfunction sendAPIQuery(target, query, callback) {\n  let redundancy;\n  let send;\n  if (typeof target === \"string\") {\n    const api = getAPIModule(target);\n    if (!api) {\n      callback(void 0, 424);\n      return emptyCallback$1;\n    }\n    send = api.send;\n    const cached = getRedundancyCache(target);\n    if (cached) {\n      redundancy = cached.redundancy;\n    }\n  } else {\n    const config = createAPIConfig(target);\n    if (config) {\n      redundancy = initRedundancy(config);\n      const moduleKey = target.resources ? target.resources[0] : \"\";\n      const api = getAPIModule(moduleKey);\n      if (api) {\n        send = api.send;\n      }\n    }\n  }\n  if (!redundancy || !send) {\n    callback(void 0, 424);\n    return emptyCallback$1;\n  }\n  return redundancy.query(query, send, callback)().abort;\n}\n\nconst cache = {};\n\nfunction emptyCallback() {\n}\nconst pendingIcons = /* @__PURE__ */ Object.create(null);\nconst iconsToLoad = /* @__PURE__ */ Object.create(null);\nconst loaderFlags = /* @__PURE__ */ Object.create(null);\nconst queueFlags = /* @__PURE__ */ Object.create(null);\nfunction loadedNewIcons(provider, prefix) {\n  if (loaderFlags[provider] === void 0) {\n    loaderFlags[provider] = /* @__PURE__ */ Object.create(null);\n  }\n  const providerLoaderFlags = loaderFlags[provider];\n  if (!providerLoaderFlags[prefix]) {\n    providerLoaderFlags[prefix] = true;\n    setTimeout(() => {\n      providerLoaderFlags[prefix] = false;\n      updateCallbacks(provider, prefix);\n    });\n  }\n}\nconst errorsCache = /* @__PURE__ */ Object.create(null);\nfunction loadNewIcons(provider, prefix, icons) {\n  function err() {\n    const key = (provider === \"\" ? \"\" : \"@\" + provider + \":\") + prefix;\n    const time = Math.floor(Date.now() / 6e4);\n    if (errorsCache[key] < time) {\n      errorsCache[key] = time;\n      console.error('Unable to retrieve icons for \"' + key + '\" because API is not configured properly.');\n    }\n  }\n  if (iconsToLoad[provider] === void 0) {\n    iconsToLoad[provider] = /* @__PURE__ */ Object.create(null);\n  }\n  const providerIconsToLoad = iconsToLoad[provider];\n  if (queueFlags[provider] === void 0) {\n    queueFlags[provider] = /* @__PURE__ */ Object.create(null);\n  }\n  const providerQueueFlags = queueFlags[provider];\n  if (pendingIcons[provider] === void 0) {\n    pendingIcons[provider] = /* @__PURE__ */ Object.create(null);\n  }\n  const providerPendingIcons = pendingIcons[provider];\n  if (providerIconsToLoad[prefix] === void 0) {\n    providerIconsToLoad[prefix] = icons;\n  } else {\n    providerIconsToLoad[prefix] = providerIconsToLoad[prefix].concat(icons).sort();\n  }\n  if (!providerQueueFlags[prefix]) {\n    providerQueueFlags[prefix] = true;\n    setTimeout(() => {\n      providerQueueFlags[prefix] = false;\n      const icons2 = providerIconsToLoad[prefix];\n      delete providerIconsToLoad[prefix];\n      const api = getAPIModule(provider);\n      if (!api) {\n        err();\n        return;\n      }\n      const params = api.prepare(provider, prefix, icons2);\n      params.forEach((item) => {\n        sendAPIQuery(provider, item, (data, error) => {\n          const storage = getStorage(provider, prefix);\n          if (typeof data !== \"object\") {\n            if (error !== 404) {\n              return;\n            }\n            const t = Date.now();\n            item.icons.forEach((name) => {\n              storage.missing[name] = t;\n            });\n          } else {\n            try {\n              const parsed = addIconSet(storage, data);\n              if (!parsed.length) {\n                return;\n              }\n              const pending = providerPendingIcons[prefix];\n              parsed.forEach((name) => {\n                delete pending[name];\n              });\n              if (cache.store) {\n                cache.store(provider, data);\n              }\n            } catch (err2) {\n              console.error(err2);\n            }\n          }\n          loadedNewIcons(provider, prefix);\n        });\n      });\n    });\n  }\n}\nconst loadIcons = (icons, callback) => {\n  const cleanedIcons = listToIcons(icons, true, allowSimpleNames());\n  const sortedIcons = sortIcons(cleanedIcons);\n  if (!sortedIcons.pending.length) {\n    let callCallback = true;\n    if (callback) {\n      setTimeout(() => {\n        if (callCallback) {\n          callback(sortedIcons.loaded, sortedIcons.missing, sortedIcons.pending, emptyCallback);\n        }\n      });\n    }\n    return () => {\n      callCallback = false;\n    };\n  }\n  const newIcons = /* @__PURE__ */ Object.create(null);\n  const sources = [];\n  let lastProvider, lastPrefix;\n  sortedIcons.pending.forEach((icon) => {\n    const provider = icon.provider;\n    const prefix = icon.prefix;\n    if (prefix === lastPrefix && provider === lastProvider) {\n      return;\n    }\n    lastProvider = provider;\n    lastPrefix = prefix;\n    sources.push({\n      provider,\n      prefix\n    });\n    if (pendingIcons[provider] === void 0) {\n      pendingIcons[provider] = /* @__PURE__ */ Object.create(null);\n    }\n    const providerPendingIcons = pendingIcons[provider];\n    if (providerPendingIcons[prefix] === void 0) {\n      providerPendingIcons[prefix] = /* @__PURE__ */ Object.create(null);\n    }\n    if (newIcons[provider] === void 0) {\n      newIcons[provider] = /* @__PURE__ */ Object.create(null);\n    }\n    const providerNewIcons = newIcons[provider];\n    if (providerNewIcons[prefix] === void 0) {\n      providerNewIcons[prefix] = [];\n    }\n  });\n  const time = Date.now();\n  sortedIcons.pending.forEach((icon) => {\n    const provider = icon.provider;\n    const prefix = icon.prefix;\n    const name = icon.name;\n    const pendingQueue = pendingIcons[provider][prefix];\n    if (pendingQueue[name] === void 0) {\n      pendingQueue[name] = time;\n      newIcons[provider][prefix].push(name);\n    }\n  });\n  sources.forEach((source) => {\n    const provider = source.provider;\n    const prefix = source.prefix;\n    if (newIcons[provider][prefix].length) {\n      loadNewIcons(provider, prefix, newIcons[provider][prefix]);\n    }\n  });\n  return callback ? storeCallback(callback, sortedIcons, sources) : emptyCallback;\n};\nconst loadIcon = (icon) => {\n  return new Promise((fulfill, reject) => {\n    const iconObj = typeof icon === \"string\" ? stringToIcon(icon) : icon;\n    loadIcons([iconObj || icon], (loaded) => {\n      if (loaded.length && iconObj) {\n        const storage = getStorage(iconObj.provider, iconObj.prefix);\n        const data = getIconFromStorage(storage, iconObj.name);\n        if (data) {\n          fulfill(data);\n          return;\n        }\n      }\n      reject(icon);\n    });\n  });\n};\n\nconst cacheVersion = \"iconify2\";\nconst cachePrefix = \"iconify\";\nconst countKey = cachePrefix + \"-count\";\nconst versionKey = cachePrefix + \"-version\";\nconst hour = 36e5;\nconst cacheExpiration = 168;\nconst config = {\n  local: true,\n  session: true\n};\nlet loaded = false;\nconst count = {\n  local: 0,\n  session: 0\n};\nconst emptyList = {\n  local: [],\n  session: []\n};\nlet _window = typeof window === \"undefined\" ? {} : window;\nfunction getGlobal(key) {\n  const attr = key + \"Storage\";\n  try {\n    if (_window && _window[attr] && typeof _window[attr].length === \"number\") {\n      return _window[attr];\n    }\n  } catch (err) {\n  }\n  config[key] = false;\n  return null;\n}\nfunction setCount(storage, key, value) {\n  try {\n    storage.setItem(countKey, value.toString());\n    count[key] = value;\n    return true;\n  } catch (err) {\n    return false;\n  }\n}\nfunction getCount(storage) {\n  const count2 = storage.getItem(countKey);\n  if (count2) {\n    const total = parseInt(count2);\n    return total ? total : 0;\n  }\n  return 0;\n}\nfunction initCache(storage, key) {\n  try {\n    storage.setItem(versionKey, cacheVersion);\n  } catch (err) {\n  }\n  setCount(storage, key, 0);\n}\nfunction destroyCache(storage) {\n  try {\n    const total = getCount(storage);\n    for (let i = 0; i < total; i++) {\n      storage.removeItem(cachePrefix + i.toString());\n    }\n  } catch (err) {\n  }\n}\nconst loadCache = () => {\n  if (loaded) {\n    return;\n  }\n  loaded = true;\n  const minTime = Math.floor(Date.now() / hour) - cacheExpiration;\n  function load(key) {\n    const func = getGlobal(key);\n    if (!func) {\n      return;\n    }\n    const getItem = (index) => {\n      const name = cachePrefix + index.toString();\n      const item = func.getItem(name);\n      if (typeof item !== \"string\") {\n        return false;\n      }\n      let valid = true;\n      try {\n        const data = JSON.parse(item);\n        if (typeof data !== \"object\" || typeof data.cached !== \"number\" || data.cached < minTime || typeof data.provider !== \"string\" || typeof data.data !== \"object\" || typeof data.data.prefix !== \"string\") {\n          valid = false;\n        } else {\n          const provider = data.provider;\n          const prefix = data.data.prefix;\n          const storage = getStorage(provider, prefix);\n          valid = addIconSet(storage, data.data).length > 0;\n        }\n      } catch (err) {\n        valid = false;\n      }\n      if (!valid) {\n        func.removeItem(name);\n      }\n      return valid;\n    };\n    try {\n      const version = func.getItem(versionKey);\n      if (version !== cacheVersion) {\n        if (version) {\n          destroyCache(func);\n        }\n        initCache(func, key);\n        return;\n      }\n      let total = getCount(func);\n      for (let i = total - 1; i >= 0; i--) {\n        if (!getItem(i)) {\n          if (i === total - 1) {\n            total--;\n          } else {\n            emptyList[key].push(i);\n          }\n        }\n      }\n      setCount(func, key, total);\n    } catch (err) {\n    }\n  }\n  for (const key in config) {\n    load(key);\n  }\n};\nconst storeCache = (provider, data) => {\n  if (!loaded) {\n    loadCache();\n  }\n  function store(key) {\n    if (!config[key]) {\n      return false;\n    }\n    const func = getGlobal(key);\n    if (!func) {\n      return false;\n    }\n    let index = emptyList[key].shift();\n    if (index === void 0) {\n      index = count[key];\n      if (!setCount(func, key, index + 1)) {\n        return false;\n      }\n    }\n    try {\n      const item = {\n        cached: Math.floor(Date.now() / hour),\n        provider,\n        data\n      };\n      func.setItem(cachePrefix + index.toString(), JSON.stringify(item));\n    } catch (err) {\n      return false;\n    }\n    return true;\n  }\n  if (!Object.keys(data.icons).length) {\n    return;\n  }\n  if (data.not_found) {\n    data = Object.assign({}, data);\n    delete data.not_found;\n  }\n  if (!store(\"local\")) {\n    store(\"session\");\n  }\n};\n\nfunction toggleBrowserCache(storage, value) {\n  switch (storage) {\n    case \"local\":\n    case \"session\":\n      config[storage] = value;\n      break;\n    case \"all\":\n      for (const key in config) {\n        config[key] = value;\n      }\n      break;\n  }\n}\n\nconst separator = /[\\s,]+/;\nfunction flipFromString(custom, flip) {\n  flip.split(separator).forEach((str) => {\n    const value = str.trim();\n    switch (value) {\n      case \"horizontal\":\n        custom.hFlip = true;\n        break;\n      case \"vertical\":\n        custom.vFlip = true;\n        break;\n    }\n  });\n}\nfunction alignmentFromString(custom, align) {\n  align.split(separator).forEach((str) => {\n    const value = str.trim();\n    switch (value) {\n      case \"left\":\n      case \"center\":\n      case \"right\":\n        custom.hAlign = value;\n        break;\n      case \"top\":\n      case \"middle\":\n      case \"bottom\":\n        custom.vAlign = value;\n        break;\n      case \"slice\":\n      case \"crop\":\n        custom.slice = true;\n        break;\n      case \"meet\":\n        custom.slice = false;\n    }\n  });\n}\n\nfunction rotateFromString(value, defaultValue = 0) {\n  const units = value.replace(/^-?[0-9.]*/, \"\");\n  function cleanup(value2) {\n    while (value2 < 0) {\n      value2 += 4;\n    }\n    return value2 % 4;\n  }\n  if (units === \"\") {\n    const num = parseInt(value);\n    return isNaN(num) ? 0 : cleanup(num);\n  } else if (units !== value) {\n    let split = 0;\n    switch (units) {\n      case \"%\":\n        split = 25;\n        break;\n      case \"deg\":\n        split = 90;\n    }\n    if (split) {\n      let num = parseFloat(value.slice(0, value.length - units.length));\n      if (isNaN(num)) {\n        return 0;\n      }\n      num = num / split;\n      return num % 1 === 0 ? cleanup(num) : 0;\n    }\n  }\n  return defaultValue;\n}\n\n/**\n * Default SVG attributes\n */\nconst svgDefaults = {\n    'xmlns': 'http://www.w3.org/2000/svg',\n    'xmlnsXlink': 'http://www.w3.org/1999/xlink',\n    'aria-hidden': true,\n    'role': 'img',\n    'style': {}, // Include style if it isn't set to add verticalAlign later\n};\n/**\n * Default values for customisations for inline icon\n */\nconst inlineDefaults = { ...defaults, inline: true };\n/**\n * Render icon\n */\nconst render = (\n// Icon must be validated before calling this function\nicon, \n// Partial properties\nprops, \n// True if icon should have vertical-align added\ninline, \n// Optional reference for SVG, extracted by React.forwardRef()\nref) => {\n    // Get default properties\n    const defaultProps = inline ? inlineDefaults : defaults;\n    // Get all customisations\n    const customisations = mergeCustomisations(defaultProps, props);\n    // Create style\n    const style = typeof props.style === 'object' && props.style !== null\n        ? props.style\n        : {};\n    // Create SVG component properties\n    const componentProps = { ...svgDefaults, ref, style };\n    // Get element properties\n    for (let key in props) {\n        const value = props[key];\n        if (value === void 0) {\n            continue;\n        }\n        switch (key) {\n            // Properties to ignore\n            case 'icon':\n            case 'style':\n            case 'children':\n            case 'onLoad':\n            case '_ref':\n            case '_inline':\n                break;\n            // Boolean attributes\n            case 'inline':\n            case 'hFlip':\n            case 'vFlip':\n                customisations[key] =\n                    value === true || value === 'true' || value === 1;\n                break;\n            // Flip as string: 'horizontal,vertical'\n            case 'flip':\n                if (typeof value === 'string') {\n                    flipFromString(customisations, value);\n                }\n                break;\n            // Alignment as string\n            case 'align':\n                if (typeof value === 'string') {\n                    alignmentFromString(customisations, value);\n                }\n                break;\n            // Color: copy to style\n            case 'color':\n                style.color = value;\n                break;\n            // Rotation as string\n            case 'rotate':\n                if (typeof value === 'string') {\n                    customisations[key] = rotateFromString(value);\n                }\n                else if (typeof value === 'number') {\n                    customisations[key] = value;\n                }\n                break;\n            // Remove aria-hidden\n            case 'ariaHidden':\n            case 'aria-hidden':\n                if (value !== true && value !== 'true') {\n                    delete componentProps['aria-hidden'];\n                }\n                break;\n            // Copy missing property if it does not exist in customisations\n            default:\n                if (defaultProps[key] === void 0) {\n                    componentProps[key] = value;\n                }\n        }\n    }\n    // Generate icon\n    const item = iconToSVG(icon, customisations);\n    // Counter for ids based on \"id\" property to render icons consistently on server and client\n    let localCounter = 0;\n    let id = props.id;\n    if (typeof id === 'string') {\n        // Convert '-' to '_' to avoid errors in animations\n        id = id.replace(/-/g, '_');\n    }\n    // Add icon stuff\n    componentProps.dangerouslySetInnerHTML = {\n        __html: replaceIDs(item.body, id ? () => id + 'ID' + localCounter++ : 'iconifyReact'),\n    };\n    for (let key in item.attributes) {\n        componentProps[key] = item.attributes[key];\n    }\n    if (item.inline && style.verticalAlign === void 0) {\n        style.verticalAlign = '-0.125em';\n    }\n    return React.createElement('svg', componentProps);\n};\n\n/**\n * Enable cache\n */\nfunction enableCache(storage) {\n    toggleBrowserCache(storage, true);\n}\n/**\n * Disable cache\n */\nfunction disableCache(storage) {\n    toggleBrowserCache(storage, false);\n}\n/**\n * Initialise stuff\n */\n// Enable short names\nallowSimpleNames(true);\n// Set API module\nsetAPIModule('', fetchAPIModule);\n/**\n * Browser stuff\n */\nif (typeof document !== 'undefined' && typeof window !== 'undefined') {\n    // Set cache and load existing cache\n    cache.store = storeCache;\n    loadCache();\n    const _window = window;\n    // Load icons from global \"IconifyPreload\"\n    if (_window.IconifyPreload !== void 0) {\n        const preload = _window.IconifyPreload;\n        const err = 'Invalid IconifyPreload syntax.';\n        if (typeof preload === 'object' && preload !== null) {\n            (preload instanceof Array ? preload : [preload]).forEach((item) => {\n                try {\n                    if (\n                    // Check if item is an object and not null/array\n                    typeof item !== 'object' ||\n                        item === null ||\n                        item instanceof Array ||\n                        // Check for 'icons' and 'prefix'\n                        typeof item.icons !== 'object' ||\n                        typeof item.prefix !== 'string' ||\n                        // Add icon set\n                        !addCollection(item)) {\n                        console.error(err);\n                    }\n                }\n                catch (e) {\n                    console.error(err);\n                }\n            });\n        }\n    }\n    // Set API from global \"IconifyProviders\"\n    if (_window.IconifyProviders !== void 0) {\n        const providers = _window.IconifyProviders;\n        if (typeof providers === 'object' && providers !== null) {\n            for (let key in providers) {\n                const err = 'IconifyProviders[' + key + '] is invalid.';\n                try {\n                    const value = providers[key];\n                    if (typeof value !== 'object' ||\n                        !value ||\n                        value.resources === void 0) {\n                        continue;\n                    }\n                    if (!addAPIProvider(key, value)) {\n                        console.error(err);\n                    }\n                }\n                catch (e) {\n                    console.error(err);\n                }\n            }\n        }\n    }\n}\nclass IconComponent extends React.Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            // Render placeholder before component is mounted\n            icon: null,\n        };\n    }\n    /**\n     * Abort loading icon\n     */\n    _abortLoading() {\n        if (this._loading) {\n            this._loading.abort();\n            this._loading = null;\n        }\n    }\n    /**\n     * Update state\n     */\n    _setData(icon) {\n        if (this.state.icon !== icon) {\n            this.setState({\n                icon,\n            });\n        }\n    }\n    /**\n     * Check if icon should be loaded\n     */\n    _checkIcon(changed) {\n        const state = this.state;\n        const icon = this.props.icon;\n        // Icon is an object\n        if (typeof icon === 'object' &&\n            icon !== null &&\n            typeof icon.body === 'string') {\n            // Stop loading\n            this._icon = '';\n            this._abortLoading();\n            if (changed || state.icon === null) {\n                // Set data if it was changed\n                this._setData({\n                    data: fullIcon(icon),\n                });\n            }\n            return;\n        }\n        // Invalid icon?\n        let iconName;\n        if (typeof icon !== 'string' ||\n            (iconName = stringToIcon(icon, false, true)) === null) {\n            this._abortLoading();\n            this._setData(null);\n            return;\n        }\n        // Load icon\n        const data = getIconData(iconName);\n        if (data === null) {\n            // Icon needs to be loaded\n            if (!this._loading || this._loading.name !== icon) {\n                // New icon to load\n                this._abortLoading();\n                this._icon = '';\n                this._setData(null);\n                this._loading = {\n                    name: icon,\n                    abort: loadIcons([iconName], this._checkIcon.bind(this, false)),\n                };\n            }\n            return;\n        }\n        // Icon data is available\n        if (this._icon !== icon || state.icon === null) {\n            // New icon or icon has been loaded\n            this._abortLoading();\n            this._icon = icon;\n            // Add classes\n            const classes = ['iconify'];\n            if (iconName.prefix !== '') {\n                classes.push('iconify--' + iconName.prefix);\n            }\n            if (iconName.provider !== '') {\n                classes.push('iconify--' + iconName.provider);\n            }\n            // Set data\n            this._setData({\n                data,\n                classes,\n            });\n            if (this.props.onLoad) {\n                this.props.onLoad(icon);\n            }\n        }\n    }\n    /**\n     * Component mounted\n     */\n    componentDidMount() {\n        this._checkIcon(false);\n    }\n    /**\n     * Component updated\n     */\n    componentDidUpdate(oldProps) {\n        if (oldProps.icon !== this.props.icon) {\n            this._checkIcon(true);\n        }\n    }\n    /**\n     * Abort loading\n     */\n    componentWillUnmount() {\n        this._abortLoading();\n    }\n    /**\n     * Render\n     */\n    render() {\n        const props = this.props;\n        const icon = this.state.icon;\n        if (icon === null) {\n            // Render placeholder\n            return props.children\n                ? props.children\n                : React.createElement('span', {});\n        }\n        // Add classes\n        let newProps = props;\n        if (icon.classes) {\n            newProps = {\n                ...props,\n                className: (typeof props.className === 'string'\n                    ? props.className + ' '\n                    : '') + icon.classes.join(' '),\n            };\n        }\n        // Render icon\n        return render(icon.data, newProps, props._inline, props._ref);\n    }\n}\n/**\n * Block icon\n *\n * @param props - Component properties\n */\nconst Icon = React.forwardRef(function Icon(props, ref) {\n    const newProps = {\n        ...props,\n        _ref: ref,\n        _inline: false,\n    };\n    return React.createElement(IconComponent, newProps);\n});\n/**\n * Inline icon (has negative verticalAlign that makes it behave like icon font)\n *\n * @param props - Component properties\n */\nconst InlineIcon = React.forwardRef(function InlineIcon(props, ref) {\n    const newProps = { ...props, _ref: ref, _inline: true };\n    return React.createElement(IconComponent, newProps);\n});\n/**\n * Internal API\n */\nconst _api = {\n    getAPIConfig,\n    setAPIModule,\n    sendAPIQuery,\n    setFetch,\n    getFetch,\n    listAPIProviders,\n    mergeParams,\n};\n\nexport { Icon, InlineIcon, _api, addAPIProvider, addCollection, addIcon, buildIcon, calculateSize, disableCache, enableCache, getIcon, iconExists, listIcons, loadIcon, loadIcons, replaceIDs, shareStorage };\n", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"component\", \"direction\", \"spacing\", \"divider\", \"children\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { createUnarySpacing, getValue, handleBreakpoints, mergeBreakpointsInOrder, unstable_extendSxProp as extendSxProp, unstable_resolveBreakpointValues as resolveBreakpointValues } from '@mui/system';\nimport { deepmerge } from '@mui/utils';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\n\n/**\n * Return an array with the separator React element interspersed between\n * each React node of the input children.\n *\n * > joinChildren([1,2,3], 0)\n * [1,0,2,0,3]\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction joinChildren(children, separator) {\n  const childrenArray = React.Children.toArray(children).filter(Boolean);\n  return childrenArray.reduce((output, child, index) => {\n    output.push(child);\n    if (index < childrenArray.length - 1) {\n      output.push( /*#__PURE__*/React.cloneElement(separator, {\n        key: `separator-${index}`\n      }));\n    }\n    return output;\n  }, []);\n}\nconst getSideFromDirection = direction => {\n  return {\n    row: 'Left',\n    'row-reverse': 'Right',\n    column: 'Top',\n    'column-reverse': 'Bottom'\n  }[direction];\n};\nexport const style = ({\n  ownerState,\n  theme\n}) => {\n  let styles = _extends({\n    display: 'flex',\n    flexDirection: 'column'\n  }, handleBreakpoints({\n    theme\n  }, resolveBreakpointValues({\n    values: ownerState.direction,\n    breakpoints: theme.breakpoints.values\n  }), propValue => ({\n    flexDirection: propValue\n  })));\n  if (ownerState.spacing) {\n    const transformer = createUnarySpacing(theme);\n    const base = Object.keys(theme.breakpoints.values).reduce((acc, breakpoint) => {\n      if (typeof ownerState.spacing === 'object' && ownerState.spacing[breakpoint] != null || typeof ownerState.direction === 'object' && ownerState.direction[breakpoint] != null) {\n        acc[breakpoint] = true;\n      }\n      return acc;\n    }, {});\n    const directionValues = resolveBreakpointValues({\n      values: ownerState.direction,\n      base\n    });\n    const spacingValues = resolveBreakpointValues({\n      values: ownerState.spacing,\n      base\n    });\n    if (typeof directionValues === 'object') {\n      Object.keys(directionValues).forEach((breakpoint, index, breakpoints) => {\n        const directionValue = directionValues[breakpoint];\n        if (!directionValue) {\n          const previousDirectionValue = index > 0 ? directionValues[breakpoints[index - 1]] : 'column';\n          directionValues[breakpoint] = previousDirectionValue;\n        }\n      });\n    }\n    const styleFromPropValue = (propValue, breakpoint) => {\n      return {\n        '& > :not(style) + :not(style)': {\n          margin: 0,\n          [`margin${getSideFromDirection(breakpoint ? directionValues[breakpoint] : ownerState.direction)}`]: getValue(transformer, propValue)\n        }\n      };\n    };\n    styles = deepmerge(styles, handleBreakpoints({\n      theme\n    }, spacingValues, styleFromPropValue));\n  }\n  styles = mergeBreakpointsInOrder(theme.breakpoints, styles);\n  return styles;\n};\nconst StackRoot = styled('div', {\n  name: 'MuiStack',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    return [styles.root];\n  }\n})(style);\nconst Stack = /*#__PURE__*/React.forwardRef(function Stack(inProps, ref) {\n  const themeProps = useThemeProps({\n    props: inProps,\n    name: 'MuiStack'\n  });\n  const props = extendSxProp(themeProps);\n  const {\n      component = 'div',\n      direction = 'column',\n      spacing = 0,\n      divider,\n      children\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = {\n    direction,\n    spacing\n  };\n  return /*#__PURE__*/_jsx(StackRoot, _extends({\n    as: component,\n    ownerState: ownerState,\n    ref: ref\n  }, other, {\n    children: divider ? joinChildren(children, divider) : children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Stack.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Defines the `flex-direction` style property.\n   * It is applied for all screen sizes.\n   * @default 'column'\n   */\n  direction: PropTypes.oneOfType([PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row']), PropTypes.arrayOf(PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row'])), PropTypes.object]),\n  /**\n   * Add an element between each child.\n   */\n  divider: PropTypes.node,\n  /**\n   * Defines the space between immediate children.\n   * @default 0\n   */\n  spacing: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n  /**\n   * The system prop, which allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Stack;"], "sourceRoot": ""}