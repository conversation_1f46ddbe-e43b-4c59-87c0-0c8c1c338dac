import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Tabs,
  Tab,
  Card,
  CardContent,
  Alert,
  CircularProgress,
  Chip,
  Button,
  Breadcrumbs,
  Link,
  Divider
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import axios from '../../utils/axios';
import Iconify from '../../components/Iconify';

// Import chart components
import IncomeOverviewChart from '../../components/admin/income/IncomeOverviewChart';
import IncomeBreakdownChart from '../../components/admin/income/IncomeBreakdownChart';
import IncomeGrowthChart from '../../components/admin/income/IncomeGrowthChart';
import IncomeTrendChart from '../../components/admin/income/IncomeTrendChart';
import IncomeFilters from '../../components/admin/income/IncomeFilters';
import IncomeExportButton from '../../components/admin/income/IncomeExportButton';
import IncomeMetricsCards from '../../components/admin/income/IncomeMetricsCards';

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`income-tabpanel-${index}`}
      aria-labelledby={`income-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

export default function IncomeDashboard() {
  const theme = useTheme();
  const { t } = useTranslation();
  const navigate = useNavigate();

  // State management
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Data state
  const [incomeStats, setIncomeStats] = useState(null);
  const [incomeByPeriod, setIncomeByPeriod] = useState([]);
  const [incomeBreakdown, setIncomeBreakdown] = useState(null);
  const [incomeGrowth, setIncomeGrowth] = useState(null);
  const [debugData, setDebugData] = useState(null);
  const [showDebug, setShowDebug] = useState(false);
  
  // Filter state
  const [filters, setFilters] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth() - 11, 1), // Last 12 months
    endDate: new Date(),
    period: 'monthly'
  });

  // Load income statistics
  const loadIncomeStats = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        startDate: filters.startDate.toISOString(),
        endDate: filters.endDate.toISOString(),
        period: filters.period
      };

      const [statsRes, periodRes, breakdownRes, growthRes] = await Promise.all([
        axios.get('/api/admin/income/statistics', { params }),
        axios.get('/api/admin/income/by-period', { params }),
        axios.get('/api/admin/income/breakdown', { params }),
        axios.get('/api/admin/income/growth', { params: { period: filters.period } })
      ]);

      if (statsRes.data.success) {
        setIncomeStats(statsRes.data.data);
      }

      if (periodRes.data.success) {
        setIncomeByPeriod(periodRes.data.data);
      }

      if (breakdownRes.data.success) {
        setIncomeBreakdown(breakdownRes.data.data);
      }

      if (growthRes.data.success) {
        setIncomeGrowth(growthRes.data.data);
      }

    } catch (err) {
      console.error('Error loading income data:', err);
      setError(err.response?.data?.message || 'Failed to load income data');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  // Load data on component mount and filter changes
  useEffect(() => {
    loadIncomeStats();
  }, [loadIncomeStats]);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
  };

  // Handle refresh
  const handleRefresh = () => {
    loadIncomeStats();
  };

  // Debug function to check recent data
  const loadDebugData = async () => {
    try {
      const response = await axios.get('/api/admin/income/debug', {
        params: { hours: 24 }
      });
      if (response.data.success) {
        setDebugData(response.data.debug);
        setShowDebug(true);
      }
    } catch (error) {
      console.error('Error loading debug data:', error);
    }
  };

  if (loading && !incomeStats) {
    return (
      <Container maxWidth="xl">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  // Navigation items for admin sections
  const navigationItems = [
    {
      title: t('menu.user_management', 'User Management'),
      path: '/admin/user-manage',
      icon: 'mdi:account-group',
      description: t('nav.user_management_desc', 'Manage users and permissions')
    },
    {
      title: t('menu.order', 'Orders'),
      path: '/admin/orders',
      icon: 'mdi:clipboard-list',
      description: t('nav.orders_desc', 'View and manage installation orders')
    },
    {
      title: t('menu.app_management', 'App Management'),
      path: '/admin/app-management',
      icon: 'mdi:cellphone-cog',
      description: t('nav.app_management_desc', 'Manage app versions and configurations')
    },
    {
      title: t('menu.statistics', 'Usage Statistics'),
      path: '/admin/statistics',
      icon: 'mdi:chart-bar',
      description: t('nav.statistics_desc', 'View system usage analytics')
    },
    {
      title: t('menu.installer_dashboard', 'Installer Dashboard'),
      path: '/installer',
      icon: 'mdi:tools',
      description: t('nav.installer_desc', 'Installer management tools')
    }
  ];

  const handleNavigate = (path) => {
    navigate(path);
  };

  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 3 }}>
        {/* Breadcrumb Navigation */}
        <Box sx={{ mb: 2 }}>
          <Breadcrumbs aria-label="breadcrumb">
            <Link
              component={RouterLink}
              to="/"
              color="inherit"
              sx={{ display: 'flex', alignItems: 'center' }}
            >
              <Iconify icon="eva:home-fill" sx={{ mr: 0.5 }} width={20} height={20} />
              {t('nav.home', 'Home')}
            </Link>
            <Link
              component={RouterLink}
              to="/admin"
              color="inherit"
              sx={{ display: 'flex', alignItems: 'center' }}
            >
              <Iconify icon="mdi:shield-account" sx={{ mr: 0.5 }} width={20} height={20} />
              {t('nav.admin', 'Admin')}
            </Link>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
              <Iconify icon="mdi:cash-multiple" sx={{ mr: 0.5 }} width={20} height={20} />
              {t('income.title', 'Income Monitoring')}
            </Typography>
          </Breadcrumbs>
        </Box>

        {/* Header with Navigation */}
        <Paper sx={{ p: 3, mb: 3, background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`, color: 'white' }}>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Iconify icon="mdi:cash-multiple" sx={{ mr: 2, fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" gutterBottom sx={{ color: 'white', mb: 0 }}>
                    {t('income.title', 'Income Monitoring Dashboard')}
                  </Typography>
                  <Typography variant="body1" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                    {t('income.subtitle', 'Track and analyze revenue from installation orders and license subscriptions')}
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', flexWrap: 'wrap', gap: 1 }}>
                {navigationItems.map((item, index) => (
                  <Button
                    key={index}
                    variant="outlined"
                    size="small"
                    startIcon={<Iconify icon={item.icon} />}
                    onClick={() => handleNavigate(item.path)}
                    sx={{
                      color: 'white',
                      borderColor: 'rgba(255,255,255,0.3)',
                      '&:hover': {
                        borderColor: 'white',
                        backgroundColor: 'rgba(255,255,255,0.1)'
                      }
                    }}
                  >
                    {item.title}
                  </Button>
                ))}
              </Box>
            </Grid>
          </Grid>
        </Paper>

        {/* Quick Navigation Cards */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          {navigationItems.map((item, index) => (
            <Grid item xs={12} sm={6} md={2.4} key={index}>
              <Card
                sx={{
                  cursor: 'pointer',
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: theme.shadows[8]
                  }
                }}
                onClick={() => handleNavigate(item.path)}
              >
                <CardContent sx={{ textAlign: 'center', py: 2 }}>
                  <Iconify
                    icon={item.icon}
                    sx={{
                      fontSize: 32,
                      color: theme.palette.primary.main,
                      mb: 1
                    }}
                  />
                  <Typography variant="subtitle2" gutterBottom>
                    {item.title}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {item.description}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Divider sx={{ mb: 3 }} />

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Filters */}
        <IncomeFilters
          filters={filters}
          onFilterChange={handleFilterChange}
          onRefresh={handleRefresh}
          loading={loading}
        />

        {/* Key Metrics Cards */}
        {incomeStats && (
          <IncomeMetricsCards
            data={incomeStats}
            growth={incomeGrowth}
            loading={loading}
          />
        )}

        {/* Main Dashboard Tabs */}
        <Paper sx={{ width: '100%', mt: 3 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab label={t('income.tabs.overview', 'Overview')} />
            <Tab label={t('income.tabs.trends', 'Trends')} />
            <Tab label={t('income.tabs.breakdown', 'Breakdown')} />
            <Tab label={t('income.tabs.growth', 'Growth Analysis')} />
          </Tabs>

          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <IncomeOverviewChart data={incomeStats} />
              </Grid>
              <Grid item xs={12} md={6}>
                <IncomeBreakdownChart data={incomeBreakdown} />
              </Grid>
              <Grid item xs={12}>
                <IncomeTrendChart 
                  data={incomeByPeriod} 
                  period={filters.period}
                />
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <IncomeTrendChart 
                  data={incomeByPeriod} 
                  period={filters.period}
                  detailed={true}
                />
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <IncomeBreakdownChart data={incomeBreakdown} detailed={true} />
              </Grid>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      {t('income.revenue_sources', 'Revenue Sources')}
                    </Typography>
                    {incomeBreakdown?.breakdown?.map((item, index) => (
                      <Box key={index} sx={{ mb: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body2">{item.label}</Typography>
                          <Chip 
                            label={`${item.percentage}%`} 
                            size="small" 
                            color={index === 0 ? 'primary' : 'secondary'}
                          />
                        </Box>
                        <Typography variant="h6" color="primary">
                          ₮{item.income.toLocaleString()}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {item.count} transactions × ₮{item.unitPrice.toLocaleString()}
                        </Typography>
                      </Box>
                    ))}
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={3}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <IncomeGrowthChart data={incomeGrowth} period={filters.period} />
              </Grid>
            </Grid>
          </TabPanel>
        </Paper>

        {/* Export and Debug Buttons */}
        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Button
            variant="outlined"
            color="info"
            onClick={loadDebugData}
            startIcon={<Iconify icon="eva:bug-outline" />}
          >
            Debug Recent Data
          </Button>
          <IncomeExportButton filters={filters} />
        </Box>

        {/* Debug Data Display */}
        {showDebug && debugData && (
          <Paper sx={{ mt: 3, p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Debug Information (Last 24 Hours)
              </Typography>
              <Button
                size="small"
                onClick={() => setShowDebug(false)}
                startIcon={<Iconify icon="eva:close-outline" />}
              >
                Close
              </Button>
            </Box>

            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      Summary
                    </Typography>
                    <Typography variant="body2">
                      <strong>Total Licenses:</strong> {debugData.summary.totalLicenses}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Total Orders:</strong> {debugData.summary.totalOrders}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Expected License Income:</strong> ₮{debugData.summary.expectedLicenseIncome.toLocaleString()}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Expected Order Income:</strong> ₮{debugData.summary.expectedOrderIncome.toLocaleString()}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Total Expected Income:</strong> ₮{debugData.summary.totalExpectedIncome.toLocaleString()}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      Recent Licenses ({debugData.recentLicenses.length})
                    </Typography>
                    <Box sx={{ maxHeight: 200, overflow: 'auto' }}>
                      {debugData.recentLicenses.map((license, index) => (
                        <Box key={index} sx={{ mb: 1, p: 1, bgcolor: 'grey.50', borderRadius: 1 }}>
                          <Typography variant="caption">
                            {license.owner} - ₮{license.cost} - {new Date(license.createdAt).toLocaleString()}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      Recent Orders ({debugData.recentOrders.length})
                    </Typography>
                    <Box sx={{ maxHeight: 200, overflow: 'auto' }}>
                      {debugData.recentOrders.map((order, index) => (
                        <Box key={index} sx={{ mb: 1, p: 1, bgcolor: 'grey.50', borderRadius: 1 }}>
                          <Typography variant="caption">
                            {order.phoneNumber} - {order.carModel} - Paid: {order.paid ? 'Yes' : 'No'} - {new Date(order.createdAt).toLocaleString()}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Paper>
        )}
      </Box>
    </Container>
  );
}
