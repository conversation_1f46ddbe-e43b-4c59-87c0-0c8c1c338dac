import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Tabs,
  Tab,
  Card,
  CardContent,
  Alert,
  CircularProgress,
  Chip
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import axios from '../../utils/axios';

// Import chart components
import IncomeOverviewChart from '../../components/admin/income/IncomeOverviewChart';
import IncomeBreakdownChart from '../../components/admin/income/IncomeBreakdownChart';
import IncomeGrowthChart from '../../components/admin/income/IncomeGrowthChart';
import IncomeTrendChart from '../../components/admin/income/IncomeTrendChart';
import IncomeFilters from '../../components/admin/income/IncomeFilters';
import IncomeExportButton from '../../components/admin/income/IncomeExportButton';
import IncomeMetricsCards from '../../components/admin/income/IncomeMetricsCards';

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`income-tabpanel-${index}`}
      aria-labelledby={`income-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

export default function IncomeDashboard() {
  const theme = useTheme();
  const { t } = useTranslation();
  
  // State management
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Data state
  const [incomeStats, setIncomeStats] = useState(null);
  const [incomeByPeriod, setIncomeByPeriod] = useState([]);
  const [incomeBreakdown, setIncomeBreakdown] = useState(null);
  const [incomeGrowth, setIncomeGrowth] = useState(null);
  
  // Filter state
  const [filters, setFilters] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth() - 11, 1), // Last 12 months
    endDate: new Date(),
    period: 'monthly'
  });

  // Load income statistics
  const loadIncomeStats = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        startDate: filters.startDate.toISOString(),
        endDate: filters.endDate.toISOString(),
        period: filters.period
      };

      const [statsRes, periodRes, breakdownRes, growthRes] = await Promise.all([
        axios.get('/api/admin/income/statistics', { params }),
        axios.get('/api/admin/income/by-period', { params }),
        axios.get('/api/admin/income/breakdown', { params }),
        axios.get('/api/admin/income/growth', { params: { period: filters.period } })
      ]);

      if (statsRes.data.success) {
        setIncomeStats(statsRes.data.data);
      }

      if (periodRes.data.success) {
        setIncomeByPeriod(periodRes.data.data);
      }

      if (breakdownRes.data.success) {
        setIncomeBreakdown(breakdownRes.data.data);
      }

      if (growthRes.data.success) {
        setIncomeGrowth(growthRes.data.data);
      }

    } catch (err) {
      console.error('Error loading income data:', err);
      setError(err.response?.data?.message || 'Failed to load income data');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  // Load data on component mount and filter changes
  useEffect(() => {
    loadIncomeStats();
  }, [loadIncomeStats]);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
  };

  // Handle refresh
  const handleRefresh = () => {
    loadIncomeStats();
  };

  if (loading && !incomeStats) {
    return (
      <Container maxWidth="xl">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h4" gutterBottom>
            {t('income.title', 'Income Monitoring Dashboard')}
          </Typography>
          <Typography variant="body1" color="text.secondary">
            {t('income.subtitle', 'Track and analyze revenue from installation orders and license subscriptions')}
          </Typography>
        </Box>

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Filters */}
        <IncomeFilters
          filters={filters}
          onFilterChange={handleFilterChange}
          onRefresh={handleRefresh}
          loading={loading}
        />

        {/* Key Metrics Cards */}
        {incomeStats && (
          <IncomeMetricsCards
            data={incomeStats}
            growth={incomeGrowth}
            loading={loading}
          />
        )}

        {/* Main Dashboard Tabs */}
        <Paper sx={{ width: '100%', mt: 3 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab label={t('income.tabs.overview', 'Overview')} />
            <Tab label={t('income.tabs.trends', 'Trends')} />
            <Tab label={t('income.tabs.breakdown', 'Breakdown')} />
            <Tab label={t('income.tabs.growth', 'Growth Analysis')} />
          </Tabs>

          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <IncomeOverviewChart data={incomeStats} />
              </Grid>
              <Grid item xs={12} md={6}>
                <IncomeBreakdownChart data={incomeBreakdown} />
              </Grid>
              <Grid item xs={12}>
                <IncomeTrendChart 
                  data={incomeByPeriod} 
                  period={filters.period}
                />
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <IncomeTrendChart 
                  data={incomeByPeriod} 
                  period={filters.period}
                  detailed={true}
                />
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <IncomeBreakdownChart data={incomeBreakdown} detailed={true} />
              </Grid>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      {t('income.revenue_sources', 'Revenue Sources')}
                    </Typography>
                    {incomeBreakdown?.breakdown?.map((item, index) => (
                      <Box key={index} sx={{ mb: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body2">{item.label}</Typography>
                          <Chip 
                            label={`${item.percentage}%`} 
                            size="small" 
                            color={index === 0 ? 'primary' : 'secondary'}
                          />
                        </Box>
                        <Typography variant="h6" color="primary">
                          ₮{item.income.toLocaleString()}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {item.count} transactions × ₮{item.unitPrice.toLocaleString()}
                        </Typography>
                      </Box>
                    ))}
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={3}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <IncomeGrowthChart data={incomeGrowth} period={filters.period} />
              </Grid>
            </Grid>
          </TabPanel>
        </Paper>

        {/* Export Button */}
        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
          <IncomeExportButton filters={filters} />
        </Box>
      </Box>
    </Container>
  );
}
