import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemAvatar from '@mui/material/ListItemAvatar';
import ListItemText from '@mui/material/ListItemText';
import DialogTitle from '@mui/material/DialogTitle';
import Dialog from '@mui/material/Dialog';
import { Box, Stack, Alert, Button, Typography, LinearProgress, CircularProgress, Divider } from '@mui/material';
import { useTranslation } from 'react-i18next';
import Iconify from '../components/Iconify';

export function PaymentDialog(props) {
    const {
        onClose,
        bankList,
        open,
        qrImage,
        paymentStatus = 'idle',
        paymentProgress = 0,
        onManualCheck,
        currentInvoice
    } = props;
    const { t } = useTranslation();

    const handleClose = () => {
        onClose();
    };

    return (
        <Dialog onClose={handleClose} open={open} fullWidth={true} maxWidth={'md'} sx={{ '& .MuiDialog-paper': { position: "fixed", bottom: 0, width: '100%', margin: 0 } }}>
            <DialogTitle>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Typography variant="h6">
                        {t('payment.choose_bank', 'Choose your bank account')}
                    </Typography>
                    {paymentStatus === 'checking' && (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <CircularProgress size={20} />
                            <Typography variant="body2" color="primary">
                                {t('payment.auto_checking', 'Auto-checking...')}
                            </Typography>
                        </Box>
                    )}
                </Box>
            </DialogTitle>

            {/* Payment Status Alert */}
            {paymentStatus === 'checking' && (
                <Box sx={{ px: 3, pb: 2 }}>
                    <Alert severity="info">
                        <Typography variant="body2" gutterBottom>
                            {t('payment.auto_verification', 'We are automatically checking your payment status. Please complete your payment using one of the options below.')}
                        </Typography>
                        <LinearProgress
                            variant="determinate"
                            value={paymentProgress}
                            sx={{ mt: 1, mb: 1 }}
                        />
                        <Typography variant="caption">
                            {t('payment.progress', 'Progress: {{progress}}%', { progress: Math.round(paymentProgress) })}
                        </Typography>
                    </Alert>
                </Box>
            )}

            {paymentStatus === 'success' && (
                <Box sx={{ px: 3, pb: 2 }}>
                    <Alert severity="success">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Iconify icon="eva:checkmark-circle-2-fill" />
                            <Typography variant="body2">
                                {t('payment.success', 'Payment confirmed! Your license has been extended.')}
                            </Typography>
                        </Box>
                    </Alert>
                </Box>
            )}

            {paymentStatus === 'failed' && (
                <Box sx={{ px: 3, pb: 2 }}>
                    <Alert severity="warning" action={
                        <Button
                            color="inherit"
                            size="small"
                            onClick={onManualCheck}
                            startIcon={<Iconify icon="eva:refresh-outline" />}
                        >
                            {t('payment.check_again', 'Check Again')}
                        </Button>
                    }>
                        <Typography variant="body2">
                            {t('payment.timeout', 'Payment verification timed out. Please check manually or complete your payment.')}
                        </Typography>
                    </Alert>
                </Box>
            )}

            {/* QR Code */}
            <Stack sx={{ width: '100%', alignItems: 'center', justifyContent: 'center', px: 3, pb: 2 }}>
                {qrImage && qrImage !== null && (
                    <Box sx={{ width: 164, height: 164, border: 1, borderColor: 'grey.300', borderRadius: 1, p: 1 }}>
                        <img src={`data:image/jpeg;base64,${qrImage}`} style={{ width: '100%', height: '100%' }} alt="QR code for payment" />
                    </Box>
                )}
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, textAlign: 'center' }}>
                    {t('payment.qr_instruction', 'Scan this QR code with your banking app or choose a bank below')}
                </Typography>
            </Stack>

            <Divider />

            {/* Bank List */}
            <List sx={{ pt: 0, maxHeight: 350, overflowY: 'scroll' }}>
                {(bankList || []).map((bank, index) => (
                    <ListItem
                        button
                        onClick={() => window.location.href = bank.link}
                        key={index}
                        sx={{
                            '&:hover': {
                                backgroundColor: 'action.hover'
                            }
                        }}
                    >
                        <ListItemAvatar>
                            <img src={`${bank.logo}`} width={50} height={50} alt={`Logo of ${bank.name}`} />
                        </ListItemAvatar>
                        <ListItemText
                            primary={bank.name}
                            secondary={bank.description}
                            primaryTypographyProps={{ fontWeight: 'medium' }}
                        />
                        <Iconify icon="eva:arrow-ios-forward-fill" />
                    </ListItem>
                ))}
            </List>

            {/* Manual Check Button */}
            {currentInvoice && paymentStatus !== 'success' && (
                <Box sx={{ p: 3, pt: 2 }}>
                    <Button
                        fullWidth
                        variant="outlined"
                        onClick={onManualCheck}
                        startIcon={<Iconify icon="eva:refresh-outline" />}
                        disabled={paymentStatus === 'checking'}
                    >
                        {t('payment.manual_check', 'Check Payment Status Manually')}
                    </Button>
                </Box>
            )}
        </Dialog>
    );
}