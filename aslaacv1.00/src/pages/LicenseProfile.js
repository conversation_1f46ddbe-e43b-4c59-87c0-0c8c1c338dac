


import { <PERSON><PERSON>,  <PERSON>rid,  Con<PERSON>er, <PERSON><PERSON><PERSON>, <PERSON><PERSON>r, TextField, Button, Chip, Select, MenuItem, FormControl, InputLabel, Alert, CircularProgress, Snackbar, LinearProgress, Box } from '@mui/material';

import { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
// hooks
import useAuth from '../hooks/useAuth';
// components
import Page from '../components/Page';
import axios from '../utils/axios';
import Layout from '../layout';
import { fDate } from '../utils/formatUtils';
import { PRICE_PER_MONTH } from '../config';
import { fShortenNumber } from '../utils/formatUtils';
import { PaymentDialog } from './PaymentDialog';
import Iconify from '../components/Iconify';

// ----------------------------------------------------------------------

export default function DeviceProfile() {
  const { initialize, user } = useAuth();
  const [qrImage,setQrImage] = useState();
  const [bankList, setBankList] = useState([]);
  const [paymentRequest, setPaymentRequest] = useState(false);
  const [totalPrice, setTotalPrice] = useState(3 * PRICE_PER_MONTH); // Default to 3 months
  const { t } = useTranslation();

  const [days, setDays] = useState(3); // Default to 3 months

  // Payment verification states
  const [currentInvoice, setCurrentInvoice] = useState(null);
  const [paymentStatus, setPaymentStatus] = useState('idle'); // idle, checking, success, failed
  const [paymentProgress, setPaymentProgress] = useState(0);
  const [showNotification, setShowNotification] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState('');
  const [notificationSeverity, setNotificationSeverity] = useState('success');
  const paymentCheckInterval = useRef(null);
  const paymentCheckAttempts = useRef(0);
  const maxPaymentCheckAttempts = 60; // Check for 5 minutes (60 * 5 seconds)

  // Payment verification functions
  const checkPaymentStatus = async (invoiceId) => {
    try {
      const response = await axios.get(`/api/hook/payment/check/${invoiceId}`);
      if (response.data.success && response.data.payment && response.data.payment.rows && response.data.payment.rows.length > 0) {
        // Payment found and confirmed

        // Try to create license manually in case webhook didn't trigger
        try {
          console.log('Attempting manual license creation:', {
            userId: user._id,
            cost: totalPrice,
            invoiceId: invoiceId
          });

          const licenseResponse = await axios.post('/api/hook/payment/create-license-manual', {
            userId: user._id,
            cost: totalPrice,
            invoiceId: invoiceId
          });

          console.log('Manual license creation response:', licenseResponse.data);

          if (licenseResponse.data.success) {
            console.log('License created successfully via manual method');
          }
        } catch (licenseError) {
          console.error('Manual license creation failed:', licenseError);
          console.error('Error response:', licenseError.response?.data);
          console.error('Error message:', licenseError.message);

          // Show detailed error to user
          const errorMessage = licenseError.response?.data?.message || licenseError.message || 'Unknown error';
          setNotificationMessage(`License creation failed: ${errorMessage}`);
          setNotificationSeverity('error');
          setShowNotification(true);
        }

        setPaymentStatus('success');
        setPaymentProgress(100);
        clearPaymentCheck();

        // Show success notification
        setNotificationMessage(t('license.payment_success', 'Payment confirmed! Your license has been extended successfully.'));
        setNotificationSeverity('success');
        setShowNotification(true);

        // Close payment dialog
        setPaymentRequest(false);

        // Refresh user data to get updated license info
        setTimeout(() => {
          initialize();
        }, 1000);

        return true;
      }
      return false;
    } catch (error) {
      console.error('Payment check error:', error);
      return false;
    }
  };

  const startPaymentCheck = (invoiceId) => {
    setPaymentStatus('checking');
    setCurrentInvoice(invoiceId);
    paymentCheckAttempts.current = 0;

    paymentCheckInterval.current = setInterval(async () => {
      paymentCheckAttempts.current += 1;
      const progress = (paymentCheckAttempts.current / maxPaymentCheckAttempts) * 100;
      setPaymentProgress(Math.min(progress, 95)); // Don't show 100% until payment is confirmed

      const paymentConfirmed = await checkPaymentStatus(invoiceId);

      if (paymentConfirmed) {
        return; // Payment confirmed, interval cleared in checkPaymentStatus
      }

      if (paymentCheckAttempts.current >= maxPaymentCheckAttempts) {
        // Timeout reached
        clearPaymentCheck();
        setPaymentStatus('failed');
        setNotificationMessage(t('license.payment_timeout', 'Payment verification timed out. Please check your payment manually or try again.'));
        setNotificationSeverity('warning');
        setShowNotification(true);
      }
    }, 5000); // Check every 5 seconds
  };

  const clearPaymentCheck = () => {
    if (paymentCheckInterval.current) {
      clearInterval(paymentCheckInterval.current);
      paymentCheckInterval.current = null;
    }
  };

  const handleLicenseKey = async () => {
    try {
      setPaymentStatus('idle');
      const response = await axios.post(`/api/license/extend-license`, { totalCost: totalPrice });
      if (response.status === 200) {
        if (response.data.data && response.data.data.bankList) {
          setQrImage(response.data.data.bankList.qr_image)
          setBankList(response.data.data.bankList.urls);
          setPaymentRequest(true);

          // Start automatic payment checking
          const invoiceId = response.data.data.invoice;
          if (invoiceId) {
            startPaymentCheck(invoiceId);
          }
        }
      }
    } catch (error) {
      console.error('License extension error:', error);
      setNotificationMessage(t('license.extension_error', 'Failed to initiate license extension. Please try again.'));
      setNotificationSeverity('error');
      setShowNotification(true);
    }
  };

  const handleChange = (e) => {
    setDays(parseInt(e.target.value, 10));
    if (e.target.value === '12') {
      setTotalPrice(7 * PRICE_PER_MONTH); // Set total price for 1 year to 7 months payment
    } else {
      setTotalPrice((e.target.value * PRICE_PER_MONTH));
    }
  };

  const handleClosePaymentDialog = () => {
    setPaymentRequest(false);
    clearPaymentCheck();
    setPaymentStatus('idle');
    setPaymentProgress(0);
  };

  const handleCloseNotification = () => {
    setShowNotification(false);
  };

  const handleManualPaymentCheck = async () => {
    if (currentInvoice) {
      setNotificationMessage(t('license.checking_payment', 'Checking payment status...'));
      setNotificationSeverity('info');
      setShowNotification(true);

      const paymentConfirmed = await checkPaymentStatus(currentInvoice);
      if (!paymentConfirmed) {
        setNotificationMessage(t('license.payment_not_found', 'Payment not yet confirmed. Please complete your payment and try again.'));
        setNotificationSeverity('warning');
        setShowNotification(true);
      }
    }
  };

  const handleDebugLicense = async () => {
    try {
      const response = await axios.get(`/api/hook/payment/debug-user-license/${user._id}`);
      console.log('Debug license info:', response.data);

      if (response.data.success) {
        const debug = response.data.debug;
        const message = `Current Expiry: ${new Date(debug.user.currentExpiry).toLocaleDateString()}\nDays Remaining: ${debug.user.daysRemaining}\nRecent Licenses: ${debug.userLicenses.length}\nLast 24h All Licenses: ${debug.allRecentLicenses.length}`;

        setNotificationMessage(message);
        setNotificationSeverity('info');
        setShowNotification(true);
      }
    } catch (error) {
      console.error('Debug license error:', error);
      console.error('Error response:', error.response?.data);

      const errorMessage = error.response?.data?.message || error.message || 'Unknown error';
      setNotificationMessage(`Debug failed: ${errorMessage}`);
      setNotificationSeverity('error');
      setShowNotification(true);
    }
  };

  const handleTestRoutes = async () => {
    try {
      const response = await axios.get('/api/hook/payment/test');
      console.log('Route test response:', response.data);

      setNotificationMessage('Routes are working! Check console for details.');
      setNotificationSeverity('success');
      setShowNotification(true);
    } catch (error) {
      console.error('Route test error:', error);
      setNotificationMessage(`Route test failed: ${error.message}`);
      setNotificationSeverity('error');
      setShowNotification(true);
    }
  };

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      clearPaymentCheck();
    };
  }, []);

  useEffect(() => {
    
  }, []);
  
  return (
    <Page title="Device Profile">
      <Layout />
      <Container sx={{ py: { xs: 12 } }} maxWidth={'sm'}>
        <Grid container spacing={3}  >

          <Grid item xs={12} >
            <Typography variant='h4' sx={{ mt: 2 }}>
              {t("device_profile.license_information")}
              <Chip sx={{ ml: 2 }} label={user?.status}
                size="small" />
            </Typography>
            <Divider sx={{ mb: 4, mt: 1 }} />

            <Stack spacing={3}>

              <TextField label={`${t("words.license")}`} disabled value={user.licenseKey} />
              <TextField label={`${t("words.expired")}`} disabled value={fDate(user.expired)} />
              <FormControl>
                <InputLabel id="period-select-label">{t("words.period")}</InputLabel>
                <Select label="Period" onChange={handleChange} value={`${days}`} labelId="period-select-label">
                  <MenuItem value='1' >1 Month</MenuItem>
                  <MenuItem value='3'>3 Months</MenuItem>
                  <MenuItem value='6'>6 Months</MenuItem>
                  <MenuItem value='12'>1 Year</MenuItem>
                  <MenuItem value='36'> Forever </MenuItem>
                </Select>
              </FormControl>
              <Typography sx={{ textAlign: 'right' }}>{t("device_profile.total_price")}: {fShortenNumber(totalPrice)}</Typography>

              {/* Payment Status Indicator */}
              {paymentStatus === 'checking' && (
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <CircularProgress size={20} />
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="body2">
                        {t('license.checking_payment_auto', 'Automatically checking payment status...')}
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={paymentProgress}
                        sx={{ mt: 1 }}
                      />
                      <Typography variant="caption" color="text.secondary">
                        {t('license.payment_check_progress', 'Checking payment... {{progress}}%', { progress: Math.round(paymentProgress) })}
                      </Typography>
                    </Box>
                  </Box>
                </Alert>
              )}

              {paymentStatus === 'success' && (
                <Alert severity="success" sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Iconify icon="eva:checkmark-circle-2-fill" />
                    <Typography variant="body2">
                      {t('license.payment_confirmed', 'Payment confirmed! License extended successfully.')}
                    </Typography>
                  </Box>
                </Alert>
              )}

              {paymentStatus === 'failed' && (
                <Alert severity="warning" sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Iconify icon="eva:clock-outline" />
                    <Typography variant="body2">
                      {t('license.payment_timeout_msg', 'Payment verification timed out. Please check manually.')}
                    </Typography>
                  </Box>
                </Alert>
              )}

              <Button
                fullWidth
                size="large"
                sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                onClick={handleLicenseKey}
                variant="contained"
                disabled={paymentStatus === 'checking'}
              >
                {paymentStatus === 'checking' ? (
                  <>
                    <CircularProgress size={20} sx={{ mr: 1 }} />
                    {t('license.processing', 'Processing...')}
                  </>
                ) : (
                  t("device_profile.request_license")
                )}
              </Button>

              {/* Debug Buttons - Remove these after testing */}
              <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                <Button
                  fullWidth
                  size="small"
                  variant="outlined"
                  onClick={handleTestRoutes}
                  startIcon={<Iconify icon="eva:checkmark-circle-outline" />}
                >
                  Test Routes
                </Button>
                <Button
                  fullWidth
                  size="small"
                  variant="outlined"
                  onClick={handleDebugLicense}
                  startIcon={<Iconify icon="eva:info-outline" />}
                >
                  Debug License
                </Button>
              </Box>
            </Stack>
          </Grid>
        </Grid>
      </Container>

      {/* Enhanced Payment Dialog */}
      {paymentRequest && (
        <PaymentDialog
          qrImage={qrImage}
          open={paymentRequest}
          onClose={handleClosePaymentDialog}
          bankList={bankList}
          paymentStatus={paymentStatus}
          paymentProgress={paymentProgress}
          onManualCheck={handleManualPaymentCheck}
          currentInvoice={currentInvoice}
        />
      )}

      {/* Notification Snackbar */}
      <Snackbar
        open={showNotification}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseNotification} severity={notificationSeverity}>
          {notificationMessage}
        </Alert>
      </Snackbar>
    </Page>
  );
}
