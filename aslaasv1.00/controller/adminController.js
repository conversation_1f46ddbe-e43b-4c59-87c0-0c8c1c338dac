const admin = require("firebase-admin");

const auth = require("../middleware/auth");
const UserModel = require("../models/user");
const DeviceModel = require("../models/device");
const OrderModel = require("../models/order");
const LicenseModel = require("../models/license");
const WalletModel = require("../models/wallet");
const carRentLog = require("../models/carRentLog");
const ObjectId = require("mongoose").Types.ObjectId;
const SimStatusLog = require("../models/simSmsLog");
const carGpsLog = require("../models/carGps"); // adjust the path as needed

// Import statistics models
const LogModel = require("../models/log");
const CommandStatistics = require("../models/commandStatistics");
const UserActivityStatistics = require("../models/userActivityStatistics");
const DeviceStatistics = require("../models/deviceStatistics");
// const statisticsCache = require("../utils/statisticsCache");



const userList = async (req, res) => {
  try {
    const { deviceNumber, phoneNumber } = req.query;
    const today = new Date();
    const todayStart = new Date(today.setHours(0, 0, 0, 0));
    const todayEnd = new Date(today.setHours(23, 59, 59, 999));

    // Initialize the matchQuery to an empty object (no filters)
    let matchQuery = {};
    let deviceMatchQuery = {};

    // If neither deviceNumber nor phoneNumber is provided, apply a default filter (e.g., limit to first 10 users)
    if (deviceNumber) {
      // For device search, we'll use this in the $unwind + $match pipeline
      deviceMatchQuery = { "devices.deviceNumber": deviceNumber };
      matchQuery = deviceMatchQuery;  // Keep for backward compatibility
    } else if (phoneNumber) {
      // For phone search, we can directly match on the user document
      matchQuery = { phoneNumber: phoneNumber };  // Filter by phoneNumber
    }

    // Set the default facet stage
    let facetStage = {
      totalUsers: [{ $count: "total" }],
      totalDevices: [
        { $unwind: "$devices" },
        { $group: { _id: null, totalDevices: { $sum: 1 } } },
      ],
      expiredUsers: [
        {
          $match: {
            expired: { $lt: todayStart },
          },
        },
        { $count: "expiredUsers" },
      ],
      todayExpiringUsers: [
        {
          $match: {
            expired: { $gte: todayStart, $lt: todayEnd },
          },
        },
        { $count: "todayExpiringUsers" },
      ],
      notExpiredUsers: [
        {
          $match: {
            expired: { $gte: todayEnd },
          },
        },
        { $count: "notExpiredUsers" },
      ],
    };

    // Only add usersData facet if phoneNumber or deviceNumber is provided
    if (deviceNumber || phoneNumber) {
      if (deviceNumber) {
        // When searching by device number, we need to unwind to filter by device properties
        facetStage.usersData = [
          { $unwind: { path: "$devices", preserveNullAndEmptyArrays: true } }, // Preserve users without devices
          { $match: deviceMatchQuery }, // Apply the device filter
          {
            $group: {
              _id: "$_id",
              phoneNumber: { $first: "$phoneNumber" },
              username: { $first: "$username" },
              status: { $first: "$status" },
              expired: { $first: "$expired" },
              pinCode: { $first: "$pinCode" },
              devices: { $first: "$devices" }, // Take the first (matched) device
            },
          },
        ];
      } else {
        // When searching by phone number, no need to unwind - just match and project
        facetStage.usersData = [
          { $match: matchQuery }, // Apply the filter to the user data if present
          {
            $addFields: {
              devices: { $arrayElemAt: ["$devices", 0] } // Get first device or null if no devices
            }
          },
          {
            $project: {
              _id: 1,
              phoneNumber: 1,
              username: 1,
              status: 1,
              expired: 1,
              devices: 1, // Return the device object (or null)
              pinCode: 1, // Add this line to include the pinCode field
            },
          },
        ];
      }
    }

    // Only add lookup stages if phoneNumber or deviceNumber is provided
    if (deviceNumber || phoneNumber) {
      facetStage.lastPayload = [
        { 
          $unwind: "$devices" 
        },
        {
          $lookup: {
            from: "cargps", 
            localField: "devices.deviceNumber",
            foreignField: "deviceNumber",
            as: "payloadData",
          }
        },
        {
          $unwind: {
            path: "$payloadData",
            preserveNullAndEmptyArrays: true,
          },
        },
        { 
          $sort: { "payloadData.createdAt": -1 } 
        },
        { 
          $limit: 1 
        },
        {
          $project: {
            lastPayload: "$payloadData.payload",
            lastPayloadCreatedAt: "$payloadData.createdAt"
          }
        }
      ];

      facetStage.lastSimcardLog = [
        { 
          $lookup: {
            from: "simcardsmslogs", 
            localField: "devices.deviceNumber", // Join using deviceNumber
            foreignField: "deviceNumber", // Match by deviceNumber
            as: "simcardLogData",
          }
        },
        {
          $unwind: {
            path: "$simcardLogData",
            preserveNullAndEmptyArrays: true,
          },
        },
        { 
          $match: { "simcardLogData.content": /Huchintei hugatsaa/i }
        },
        { 
          $sort: { "simcardLogData.received": -1 }
        },
        { 
          $limit: 1 
        },
        {
          $project: {
            lastSimcardLog: "$simcardLogData.content",
            lastSimcardLogReceivedAt: "$simcardLogData.received"
          }
        }
      ];
    }

    const result = await UserModel.aggregate([
      {
        $lookup: {
          from: "devices",
          localField: "phoneNumber",
          foreignField: "phoneNumber",
          as: "devices",
        },
      },
      {
        // Only apply phone number filter at this stage, device filtering happens in facet
        $match: phoneNumber ? { phoneNumber: phoneNumber } : {}
      },
      {
        $facet: facetStage // Only apply the facet stage with the necessary data
      },
      {
        $project: {
          totalUsers: { $arrayElemAt: ["$totalUsers.total", 0] },
          totalDevices: { $arrayElemAt: ["$totalDevices.totalDevices", 0] },
          expiredUsers: { $arrayElemAt: ["$expiredUsers.expiredUsers", 0] },
          todayExpiringUsers: { $arrayElemAt: ["$todayExpiringUsers.todayExpiringUsers", 0] },
          notExpiredUsers: { $arrayElemAt: ["$notExpiredUsers.notExpiredUsers", 0] },
          users: "$usersData", // Ensure you return the user data with devices
          lastPayload: "$lastPayload",
          lastSimcardLog: "$lastSimcardLog"
        },
      },
    ]);

    const data = result[0];

    return res.json({
      success: true,
      totalUsers: data.totalUsers || 0,
      totalDevices: data.totalDevices || 0,
      expiredUsers: data.expiredUsers || 0,
      todayExpiringUsers: data.todayExpiringUsers || 0,
      notExpiredUsers: data.notExpiredUsers || 0,
      users: data.users || [],
      lastPayload: data.lastPayload || null,
      lastSimcardLog: data.lastSimcardLog || null
    });
  } catch (err) {
    console.log(err);
    return res.json({ success: false, err });
  }
};
const walletChange = async (req, res) => {
  try {
    const { _id, amount, status, user } = req.body;
    await WalletModel.updateOne(
      { user, "requests._id": ObjectId(_id) },
      {
        $set: { "requests.$.status": status },
        $inc: { currentBalance: parseInt(amount) },
      }
    );
    if (amount > 0)
      await UserModel.updateOne(
        { _id: ObjectId(user) },
        { $inc: { balance: parseInt(amount) } }
      );
    res.json({ success: true });
  } catch (err) {
    res.json({ success: false });
  }
};

const walletRequest = async (req, res) => {
  console.log("Wallet request.......");
  try {
    const trs = await WalletModel.aggregate([
      {
        $lookup: {
          from: "users",
          let: { userId: "$user" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: [
                    {
                      $toString: "$_id",
                    },
                    "$$userId",
                  ],
                },
              },
            },
          ],
          as: "_user",
        },
      },
      {
        $unwind: {
          path: "$_user",
        },
      },
    ]);
    const requests = [];
    trs.forEach((t) => {
      t.requests.forEach((tr) => {
        requests.push({
          _id: `${tr._id}`,
          username: t?._user?.username,
          phoneNumber: t?._user?.phoneNumber,
          ts: tr?.ts,
          before: tr?.currentBalance || 0,
          amount: tr?.amount,
          status: tr?.status,
          balance: t.currentBalance,
          user: t?._user?._id,
          bankName: t?.bankName,
          bankAccount: t?.bankAccount,
        });
      });
    });

    return res.json({
      success: true,
      requests: requests.sort((a, b) => b.ts - a.ts),
    });
  } catch (err) {
    console.log(err);
    return res.json({ success: false, err });
  }
};

const rentCarStatus = async (req, res) => {
  try {
    const logs = await carRentLog.aggregate([
      {
        $lookup: {
          from: "users",
          let: { userId: "$renter" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: [
                    {
                      $toString: "$_id",
                    },
                    "$$userId",
                  ],
                },
              },
            },
          ],
          as: "_user",
        },
      },
      {
        $unwind: {
          path: "$_user",
        },
      },
      {
        $sort: { from: -1 },
      },
    ]);

    return res.json({ success: true, logs });
  } catch (err) {
    console.log(err);
    return res.json({ success: false, err });
  }
};

const walletTransactions = async (req, res) => {
  try {
    const trs = await WalletModel.aggregate([
      {
        $lookup: {
          from: "users",
          let: { userId: "$user" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: [
                    {
                      $toString: "$_id",
                    },
                    "$$userId",
                  ],
                },
              },
            },
          ],
          as: "_user",
        },
      },
      {
        $unwind: {
          path: "$_user",
        },
      },
    ]);
    const transactions = [];
    trs.forEach((t) => {
      console.log(t);
      t.transactions.forEach((tr) => {
        transactions.push({
          _id: `${t.user}${tr.ts}`,
          username: t?._user?.username,
          phoneNumber: t?._user?.phoneNumber,
          ts: tr?.ts,
          before: tr?.before || 0,
          amount: tr?.amount,
          mode: tr?.mode,
          description: tr?.description,
          bankName: t.bankName,
          bankAccount: t.bankAccount,
        });
      });
    });

    return res.json({
      success: true,
      transactions: transactions.sort((a, b) => b.ts - a.ts),
    });
  } catch (err) {
    console.log(err);
    return res.json({ success: false, err });
  }
};

const removeUsers = async (req, res) => {
  try {
    // Find and delete the user with the given phoneNumber
    await UserModel.deleteOne({ phoneNumber: req.body.phoneNumber });
    return res.json({ success: true });
  } catch (err) {
    console.log(err);
    return res.json({
      success: false,
      err,
    });
  }
};

const changeActive = async (req, res) => {
  try {
    // console.log(await UserModel.find({_id:{$in:req.body.ids}}));
    await UserModel.updateMany(
      { _id: { $in: req.body.ids } },
      {
        $set: {
          status: req.body.status,
        },
      }
    );
    return res.json({ success: true });
  } catch (err) {
    console.log(err);
    return res.json({
      success: false,
      err,
    });
  }
};

const driverLicenseVerification = async (req, res) => {
  try {
    const { user, verification } = req.body;
    // console.log(await UserModel.find({_id:{$in:req.body.ids}}));
    await UserModel.updateOne(
      { _id: user },
      {
        $set: {
          driverLicenseVerification: verification,
        },
      }
    );
    return res.json({ success: true });
  } catch (err) {
    console.log(err);
    return res.json({
      success: false,
      err,
    });
  }
};

const extendsLicense = async (req, res) => {
  try {
    const { expired, user, licenseKey } = req.body;
    // console.log(await UserModel.find({_id:{$in:req.body.ids}}));
    await UserModel.updateOne(
      { _id: user },
      {
        $set: {
          expired: new Date(expired),
        },
      }
    );
    await LicenseModel.updateOne(
      { _id: user, licenseKey },
      {
        $set: {
          expired: new Date(expired),
        },
      }
    );
    return res.json({ success: true });
  } catch (err) {
    console.log(err);
    return res.json({
      success: false,
      err,
    });
  }
};
const sendNotifications = async (req, res) => {
  try {
    const users = req.body.users;
    let sent = 0;
    if (users.length > 0) {
      for(const u of users){
        const user = await UserModel.findById(u);
        if (user != null) {
          const devices = await DeviceModel.find({
            phoneNumber: user.phoneNumber,
            isDefault: true,
          });
          if (devices != null && devices.length > 0) {
      
            const fmcToken = devices[0].fmctoken;
            if (fmcToken) {
              const message = {
                notification: {
                  title:req.body.title,
                  body:req.body.body,
                },
                token: fmcToken,
              };
              console.log(message)
              try {
                await admin.messaging().send(message);
                sent++;
              } catch (err) {
                console.log("notification send error",err);
              }
            }
          }
        }
      };
    }
    return res.json({ success: true, sent });
  } catch (err) {
    console.log(err);
    return res.json({ success: false, sent: 0 });
  }
};
const orderList = async (req, res) => {
  try {
    const orders = await OrderModel.aggregate([
      {
        $lookup: {
          from: "orders",
          localField: "phoneNumber",
          foreignField: "phoneNumber",
          as: "orders",
        },
      },
    ]);
    orders.map((order) => {});
    return res.json({ success: true, orders });
  } catch (err) {
    console.log(err);
    return res.json({ success: false, err });
  }
};

const resetUserPin = async (req, res) => {
  try {
    const { phoneNumber } = req.body;

    if (!phoneNumber) {
      return res.json({
        success: false,
        message: "Phone number is required"
      });
    }

    // Find and update the user's PIN to '0000'
    const user = await UserModel.findOneAndUpdate(
      { phoneNumber: phoneNumber },
      { $set: { pinCode: '0000' } },
      { new: true }
    );

    if (!user) {
      return res.json({
        success: false,
        message: "User not found"
      });
    }

    return res.json({
      success: true,
      message: "PIN reset to 0000 successfully"
    });
  } catch (err) {
    console.log(err);
    return res.json({
      success: false,
      message: "Failed to reset PIN",
      err
    });
  }
};

const setUserRole = async (req, res) => {
  try {
    const { phoneNumber, role } = req.body;

    if (!phoneNumber) {
      return res.json({
        success: false,
        message: "Phone number is required"
      });
    }

    if (!role || !['user', 'admin', 'installer'].includes(role)) {
      return res.json({
        success: false,
        message: "Valid role is required (user, admin, or installer)"
      });
    }

    // Only admin can set roles
    if (req.user.role !== 'admin') {
      return res.json({
        success: false,
        message: "Only administrators can change user roles"
      });
    }

    // Find and update the user's role
    const user = await UserModel.findOneAndUpdate(
      { phoneNumber: phoneNumber },
      { $set: { role: role } },
      { new: true }
    );

    if (!user) {
      return res.json({
        success: false,
        message: "User not found"
      });
    }

    return res.json({
      success: true,
      message: `User role updated to ${role} successfully`
    });
  } catch (err) {
    console.log(err);
    return res.json({
      success: false,
      message: "Failed to update user role",
      err
    });
  }
};

// Statistics API endpoints

// Get command statistics with filtering
const getCommandStatistics = async (req, res) => {
  try {
    const {
      period = 'daily',
      startDate,
      endDate,
      deviceNumber,
      userId,
      commandType
    } = req.query;

    // Create cache key from filters
    const filters = { period, startDate, endDate, deviceNumber, userId, commandType };

    // Cache temporarily disabled
    // const cachedData = statisticsCache.getCommandStats(filters);
    // if (cachedData) {
    //   console.log('Statistics: Serving command stats from cache');
    //   return res.json({
    //     success: true,
    //     data: cachedData,
    //     cached: true
    //   });
    // }

    // Build match conditions
    let matchConditions = {};

    if (startDate && endDate) {
      matchConditions.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    if (deviceNumber) {
      matchConditions.deviceNumber = deviceNumber;
    }

    if (userId) {
      matchConditions.userId = new ObjectId(userId);
    }

    if (commandType) {
      matchConditions.commandType = commandType;
    }

    // Aggregate command statistics
    const stats = await LogModel.aggregate([
      { $match: matchConditions },
      {
        $group: {
          _id: null,
          totalCommands: { $sum: 1 },
          successfulCommands: { $sum: { $cond: [{ $eq: ["$success", true] }, 1, 0] } },
          failedCommands: { $sum: { $cond: [{ $eq: ["$success", false] }, 1, 0] } },
          avgResponseTime: { $avg: "$responseTime" },
          minResponseTime: { $min: "$responseTime" },
          maxResponseTime: { $max: "$responseTime" },
          commandTypes: {
            $push: "$commandType"
          },
          responseStatuses: {
            $push: "$responseStatus"
          }
        }
      }
    ]);

    // Get command type breakdown
    const commandTypeStats = await LogModel.aggregate([
      { $match: matchConditions },
      {
        $group: {
          _id: "$commandType",
          count: { $sum: 1 },
          successCount: { $sum: { $cond: [{ $eq: ["$success", true] }, 1, 0] } },
          failureCount: { $sum: { $cond: [{ $eq: ["$success", false] }, 1, 0] } },
          avgResponseTime: { $avg: "$responseTime" }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Get failure reason breakdown
    const failureStats = await LogModel.aggregate([
      {
        $match: {
          ...matchConditions,
          success: false
        }
      },
      {
        $group: {
          _id: "$responseStatus",
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } }
    ]);

    const result = {
      overview: stats[0] || {
        totalCommands: 0,
        successfulCommands: 0,
        failedCommands: 0,
        avgResponseTime: 0,
        minResponseTime: 0,
        maxResponseTime: 0
      },
      commandTypes: commandTypeStats,
      failureReasons: failureStats,
      period,
      dateRange: { startDate, endDate }
    };

    // Cache temporarily disabled
    // statisticsCache.setCommandStats(filters, result);

    return res.json({
      success: true,
      data: result,
      cached: false
    });
  } catch (err) {
    console.log(err);
    return res.json({ success: false, error: err.message });
  }
};

// Get top users by command count
const getTopUsers = async (req, res) => {
  try {
    const {
      limit = 10,
      startDate,
      endDate,
      period = 'daily'
    } = req.query;

    let matchConditions = { period: period };

    if (startDate && endDate) {
      matchConditions.date = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    } else if (period === 'daily') {
      // Default to today if no date range specified
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      matchConditions.date = { $gte: today };
    }

    // Use UserActivityStatistics instead of LogModel for real-time data
    const topUsers = await UserActivityStatistics.aggregate([
      { $match: matchConditions },
      {
        $group: {
          _id: "$userId",
          phoneNumber: { $first: "$phoneNumber" },
          totalCommands: { $sum: "$commandStats.totalCommands" },
          successfulCommands: { $sum: "$commandStats.successfulCommands" },
          failedCommands: { $sum: "$commandStats.failedCommands" },
          avgResponseTime: { $avg: "$responseTimes.avgResponseTime" },
          lastActivity: { $max: "$lastActivity" },
          // Command type breakdown
          powerOnCommands: { $sum: "$commandTypes.power_on" },
          powerOffCommands: { $sum: "$commandTypes.power_off" },
          lockCommands: { $sum: "$commandTypes.lock" },
          unlockCommands: { $sum: "$commandTypes.unlock" },
          locationCommands: { $sum: "$commandTypes.location" },
          statusCommands: { $sum: "$commandTypes.status" },
          configCommands: { $sum: "$commandTypes.config" },
          otherCommands: { $sum: "$commandTypes.other" }
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "_id",
          foreignField: "_id",
          as: "userInfo"
        }
      },
      {
        $addFields: {
          successRate: {
            $cond: [
              { $gt: ["$totalCommands", 0] },
              {
                $multiply: [
                  { $divide: ["$successfulCommands", "$totalCommands"] },
                  100
                ]
              },
              0
            ]
          },
          username: { $arrayElemAt: ["$userInfo.username", 0] },
          userPhoneNumber: { $arrayElemAt: ["$userInfo.phoneNumber", 0] }
        }
      },
      { $sort: { totalCommands: -1 } },
      { $limit: parseInt(limit) }
    ]);

    return res.json({
      success: true,
      data: topUsers,
      period,
      dateRange: { startDate, endDate },
      cached: false,
      timestamp: new Date()
    });
  } catch (err) {
    console.log(err);
    return res.json({ success: false, error: err.message });
  }
};

// Get device response analytics
const getDeviceAnalytics = async (req, res) => {
  try {
    const {
      startDate,
      endDate,
      deviceNumber,
      limit = 20
    } = req.query;

    let matchConditions = {};

    if (startDate && endDate) {
      matchConditions.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    if (deviceNumber) {
      matchConditions.deviceNumber = deviceNumber;
    }

    const deviceStats = await LogModel.aggregate([
      { $match: matchConditions },
      {
        $group: {
          _id: "$deviceNumber",
          deviceType: { $first: "$deviceType" },
          totalCommands: { $sum: 1 },
          successfulCommands: { $sum: { $cond: [{ $eq: ["$success", true] }, 1, 0] } },
          failedCommands: { $sum: { $cond: [{ $eq: ["$success", false] }, 1, 0] } },
          avgResponseTime: { $avg: "$responseTime" },
          minResponseTime: { $min: "$responseTime" },
          maxResponseTime: { $max: "$responseTime" },
          timeoutCount: {
            $sum: { $cond: [{ $eq: ["$responseStatus", "timeout"] }, 1, 0] }
          },
          offlineCount: {
            $sum: { $cond: [{ $eq: ["$responseStatus", "device_offline"] }, 1, 0] }
          },
          lastActivity: { $max: "$createdAt" },
          avgSignalStrength: { $avg: "$signalStrength" }
        }
      },
      {
        $addFields: {
          successRate: {
            $multiply: [
              { $divide: ["$successfulCommands", "$totalCommands"] },
              100
            ]
          },
          responseCategory: {
            $switch: {
              branches: [
                { case: { $lt: ["$avgResponseTime", 1000] }, then: "fast" },
                { case: { $lt: ["$avgResponseTime", 5000] }, then: "medium" },
                { case: { $gte: ["$avgResponseTime", 5000] }, then: "slow" }
              ],
              default: "unknown"
            }
          }
        }
      },
      { $sort: { totalCommands: -1 } },
      { $limit: parseInt(limit) }
    ]);

    // Get response time distribution
    const responseTimeDistribution = await LogModel.aggregate([
      { $match: { ...matchConditions, responseTime: { $ne: null } } },
      {
        $bucket: {
          groupBy: "$responseTime",
          boundaries: [0, 1000, 3000, 5000, 10000, Infinity],
          default: "Other",
          output: {
            count: { $sum: 1 },
            avgResponseTime: { $avg: "$responseTime" }
          }
        }
      }
    ]);

    return res.json({
      success: true,
      data: {
        devices: deviceStats,
        responseTimeDistribution,
        dateRange: { startDate, endDate }
      }
    });
  } catch (err) {
    console.log(err);
    return res.json({ success: false, error: err.message });
  }
};

// Get time-based statistics for charts
const getTimeBasedStatistics = async (req, res) => {
  try {
    const {
      period = 'daily',
      startDate,
      endDate,
      groupBy = 'hour' // hour, day, week, month
    } = req.query;

    let matchConditions = {};

    if (startDate && endDate) {
      matchConditions.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    let groupByExpression;
    switch (groupBy) {
      case 'hour':
        groupByExpression = {
          year: { $year: "$createdAt" },
          month: { $month: "$createdAt" },
          day: { $dayOfMonth: "$createdAt" },
          hour: { $hour: "$createdAt" }
        };
        break;
      case 'day':
        groupByExpression = {
          year: { $year: "$createdAt" },
          month: { $month: "$createdAt" },
          day: { $dayOfMonth: "$createdAt" }
        };
        break;
      case 'week':
        groupByExpression = {
          year: { $year: "$createdAt" },
          week: { $week: "$createdAt" }
        };
        break;
      case 'month':
        groupByExpression = {
          year: { $year: "$createdAt" },
          month: { $month: "$createdAt" }
        };
        break;
      default:
        groupByExpression = {
          year: { $year: "$createdAt" },
          month: { $month: "$createdAt" },
          day: { $dayOfMonth: "$createdAt" }
        };
    }

    const timeStats = await LogModel.aggregate([
      { $match: matchConditions },
      {
        $group: {
          _id: groupByExpression,
          totalCommands: { $sum: 1 },
          successfulCommands: { $sum: { $cond: [{ $eq: ["$success", true] }, 1, 0] } },
          failedCommands: { $sum: { $cond: [{ $eq: ["$success", false] }, 1, 0] } },
          avgResponseTime: { $avg: "$responseTime" },
          uniqueUsers: { $addToSet: "$userId" },
          uniqueDevices: { $addToSet: "$deviceNumber" }
        }
      },
      {
        $addFields: {
          successRate: {
            $multiply: [
              { $divide: ["$successfulCommands", "$totalCommands"] },
              100
            ]
          },
          uniqueUserCount: { $size: "$uniqueUsers" },
          uniqueDeviceCount: { $size: "$uniqueDevices" }
        }
      },
      { $sort: { "_id": 1 } }
    ]);

    return res.json({
      success: true,
      data: timeStats,
      groupBy,
      period,
      dateRange: { startDate, endDate }
    });
  } catch (err) {
    console.log(err);
    return res.json({ success: false, error: err.message });
  }
};

// Get detailed command logs with filtering and pagination
const getDetailedCommandLogs = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      startDate,
      endDate,
      deviceNumber,
      userId,
      commandType,
      responseStatus,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build match conditions
    let matchConditions = {};

    if (startDate && endDate) {
      matchConditions.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    if (deviceNumber) {
      matchConditions.deviceNumber = deviceNumber;
    }

    if (userId) {
      matchConditions.userId = new ObjectId(userId);
    }

    if (commandType) {
      matchConditions.commandType = commandType;
    }

    if (responseStatus) {
      matchConditions.responseStatus = responseStatus;
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Sort configuration
    const sortConfig = {};
    sortConfig[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Optimize query with projection to reduce data transfer
    const projection = {
      user: 1,
      userId: 1,
      deviceNumber: 1,
      deviceType: 1,
      command: 1,
      commandType: 1,
      success: 1,
      responseStatus: 1,
      failureReason: 1,
      sentTime: 1,
      receiveTime: 1,
      responseTime: 1,
      response: 1,
      message: 1,
      responseType: 1,
      deviceOnline: 1,
      signalStrength: 1,
      createdAt: 1
    };

    // Use Promise.all for parallel execution
    const [logs, totalLogs] = await Promise.all([
      LogModel.find(matchConditions, projection)
        .sort(sortConfig)
        .skip(skip)
        .limit(parseInt(limit))
        .lean(),
      LogModel.countDocuments(matchConditions)
    ]);

    return res.json({
      success: true,
      data: {
        logs,
        pagination: {
          total: totalLogs,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(totalLogs / parseInt(limit))
        }
      }
    });
  } catch (err) {
    console.log(err);
    return res.json({ success: false, error: err.message });
  }
};

// Development helper - Create sample data for testing
const createSampleData = async (req, res) => {
  try {
    // Only allow in development or for admin users
    if (process.env.NODE_ENV === 'production' && req.user.role !== 'admin') {
      return res.status(403).json({ success: false, error: 'Not allowed in production' });
    }

    const mongoose = require('mongoose');

    const sampleLogs = [
      {
        user: 'sample_user_1',
        // userId: new mongoose.Types.ObjectId(), // Skip userId for sample data
        deviceNumber: '12345',
        command: 'POWER_ON',
        commandType: 'power_on',
        sent: 'yes',
        success: true,
        sentTime: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
        receiveTime: new Date(Date.now() - 1000 * 60 * 60 * 2 + 2000),
        responseTime: 2000,
        response: 'OK',
        responseType: 'MQTT',
        deviceOnline: true,
        responseStatus: 'success',
        message: 'Sample power on command'
      },
      {
        user: 'sample_user_2',
        // userId: new mongoose.Types.ObjectId(), // Skip userId for sample data
        deviceNumber: '67890',
        command: 'LOCK',
        commandType: 'lock',
        sent: 'yes',
        success: true,
        sentTime: new Date(Date.now() - 1000 * 60 * 60 * 1),
        receiveTime: new Date(Date.now() - 1000 * 60 * 60 * 1 + 1500),
        responseTime: 1500,
        response: 'SUCCESS',
        responseType: 'MQTT',
        deviceOnline: true,
        responseStatus: 'success',
        message: 'Sample lock command'
      },
      {
        user: 'sample_user_1',
        // userId: new mongoose.Types.ObjectId(), // Skip userId for sample data
        deviceNumber: '12345',
        command: 'GET_STATUS',
        commandType: 'status',
        sent: 'yes',
        success: false,
        sentTime: new Date(Date.now() - 1000 * 60 * 30),
        receiveTime: new Date(Date.now() - 1000 * 60 * 30 + 30000),
        responseTime: 30000,
        response: 'TIMEOUT',
        responseType: 'MQTT',
        deviceOnline: false,
        responseStatus: 'timeout',
        failureReason: 'Device did not respond within timeout period',
        message: 'Sample failed status command'
      },
      {
        user: 'sample_user_3',
        // userId: new mongoose.Types.ObjectId(), // Skip userId for sample data
        deviceNumber: '11111',
        command: 'UNLOCK',
        commandType: 'unlock',
        sent: 'yes',
        success: true,
        sentTime: new Date(Date.now() - 1000 * 60 * 15),
        receiveTime: new Date(Date.now() - 1000 * 60 * 15 + 1200),
        responseTime: 1200,
        response: 'DONE',
        responseType: 'MQTT',
        deviceOnline: true,
        responseStatus: 'success',
        message: 'Sample unlock command'
      },
      {
        user: 'sample_user_2',
        // userId: new mongoose.Types.ObjectId(), // Skip userId for sample data
        deviceNumber: '67890',
        command: 'GET_LOCATION',
        commandType: 'location',
        sent: 'yes',
        success: true,
        sentTime: new Date(Date.now() - 1000 * 60 * 5),
        receiveTime: new Date(Date.now() - 1000 * 60 * 5 + 3000),
        responseTime: 3000,
        response: 'OK',
        responseType: 'MQTT',
        deviceOnline: true,
        responseStatus: 'success',
        message: 'Sample location command'
      }
    ];

    // Remove existing sample data
    await LogModel.deleteMany({ message: { $regex: /^Sample/ } });

    // Insert new sample data
    await LogModel.insertMany(sampleLogs);

    // Get count
    const count = await LogModel.countDocuments({ message: { $regex: /^Sample/ } });

    res.json({
      success: true,
      message: `Created ${count} sample log entries`,
      data: { count }
    });

  } catch (err) {
    console.error('Error creating sample data:', err);
    res.status(500).json({ success: false, error: err.message });
  }
};

// Log command from frontend for statistics
const logCommand = async (req, res) => {
  try {
    const { deviceNumber, command, commandType, method } = req.body;
    const userId = req.user.id;

    if (!deviceNumber || !command) {
      return res.status(400).json({ success: false, error: 'Device number and command are required' });
    }

    // Create log entry
    const log = new LogModel({
      user: req.user.phoneNumber || req.user.username || 'unknown',
      userId: userId,
      deviceNumber: deviceNumber,
      command: command,
      commandType: commandType || 'other',
      sent: 'yes',
      success: null, // Will be updated when response is received
      sentTime: new Date(),
      responseType: method || 'MQTT',
      deviceOnline: true,
      message: `Command sent via ${method || 'MQTT'}`
    });

    await log.save();

    res.json({
      success: true,
      message: 'Command logged successfully',
      logId: log._id
    });

  } catch (err) {
    console.error('Error logging command:', err);
    res.status(500).json({ success: false, error: err.message });
  }
};

// SSL Certificate Validation File Management
const uploadSslValidationFile = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    const { filename, originalname, size } = req.file;

    res.json({
      success: true,
      message: 'SSL validation file uploaded successfully',
      file: {
        filename,
        originalname,
        size,
        uploadedAt: new Date(),
        accessUrl: `/.well-known/pki-validation/${filename}`
      }
    });

  } catch (err) {
    console.error('Error uploading SSL validation file:', err);
    res.status(500).json({ success: false, error: err.message });
  }
};

const listSslValidationFiles = async (req, res) => {
  try {
    const fs = require('fs');
    const path = require('path');
    const certsDir = path.join(__dirname, '../routes/api/certs');

    if (!fs.existsSync(certsDir)) {
      return res.json({
        success: true,
        files: []
      });
    }

    const files = fs.readdirSync(certsDir).map(filename => {
      const filePath = path.join(certsDir, filename);
      const stats = fs.statSync(filePath);

      return {
        filename,
        size: stats.size,
        uploadedAt: stats.mtime,
        accessUrl: `/.well-known/pki-validation/${filename}`
      };
    });

    res.json({
      success: true,
      files
    });

  } catch (err) {
    console.error('Error listing SSL validation files:', err);
    res.status(500).json({ success: false, error: err.message });
  }
};

const deleteSslValidationFile = async (req, res) => {
  try {
    const { filename } = req.params;
    const fs = require('fs');
    const path = require('path');
    const filePath = path.join(__dirname, '../routes/api/certs', filename);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: 'File not found'
      });
    }

    fs.unlinkSync(filePath);

    res.json({
      success: true,
      message: 'SSL validation file deleted successfully'
    });

  } catch (err) {
    console.error('Error deleting SSL validation file:', err);
    res.status(500).json({ success: false, error: err.message });
  }
};

// ==================== INCOME MONITORING FUNCTIONS ====================

/**
 * Get comprehensive income statistics for the dashboard
 */
const getIncomeStatistics = async (req, res) => {
  try {
    const { startDate, endDate, period = 'monthly' } = req.query;

    // Set default date range if not provided
    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(end.getFullYear(), end.getMonth() - 11, 1); // Last 12 months

    // Get order income (installation payments)
    const orderIncome = await OrderModel.aggregate([
      {
        $match: {
          paid: true,
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: null,
          totalOrders: { $sum: 1 },
          totalOrderIncome: { $sum: 20000 } // Fixed order price
        }
      }
    ]);

    // Get license income (subscription payments)
    const licenseIncome = await LicenseModel.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: null,
          totalLicenses: { $sum: 1 },
          totalLicenseIncome: { $sum: 5000 } // Fixed monthly price
        }
      }
    ]);

    const orderData = orderIncome[0] || { totalOrders: 0, totalOrderIncome: 0 };
    const licenseData = licenseIncome[0] || { totalLicenses: 0, totalLicenseIncome: 0 };

    const totalIncome = orderData.totalOrderIncome + licenseData.totalLicenseIncome;
    const totalTransactions = orderData.totalOrders + licenseData.totalLicenses;

    // Calculate average income
    const days = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
    const averageDailyIncome = totalIncome / Math.max(days, 1);

    res.json({
      success: true,
      data: {
        totalIncome,
        totalTransactions,
        averageDailyIncome,
        orderIncome: orderData,
        licenseIncome: licenseData,
        period: {
          start,
          end,
          days
        }
      }
    });
  } catch (error) {
    console.error('Error getting income statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get income statistics',
      error: error.message
    });
  }
};

/**
 * Get income data grouped by time period (daily, monthly, yearly)
 */
const getIncomeByPeriod = async (req, res) => {
  try {
    const { startDate, endDate, period = 'monthly' } = req.query;

    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(end.getFullYear(), end.getMonth() - 11, 1);

    // Define grouping format based on period
    let groupFormat;
    let sortField;

    switch (period) {
      case 'daily':
        groupFormat = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
        sortField = { '_id.year': 1, '_id.month': 1, '_id.day': 1 };
        break;
      case 'yearly':
        groupFormat = {
          year: { $year: '$createdAt' }
        };
        sortField = { '_id.year': 1 };
        break;
      default: // monthly
        groupFormat = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        };
        sortField = { '_id.year': 1, '_id.month': 1 };
    }

    // Get order income by period
    const ordersByPeriod = await OrderModel.aggregate([
      {
        $match: {
          paid: true,
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: groupFormat,
          orderCount: { $sum: 1 },
          orderIncome: { $sum: 20000 }
        }
      },
      { $sort: sortField }
    ]);

    // Get license income by period
    const licensesByPeriod = await LicenseModel.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: groupFormat,
          licenseCount: { $sum: 1 },
          licenseIncome: { $sum: 5000 }
        }
      },
      { $sort: sortField }
    ]);

    // Combine and format the data
    const periodMap = new Map();

    // Add order data
    ordersByPeriod.forEach(item => {
      const key = JSON.stringify(item._id);
      periodMap.set(key, {
        period: item._id,
        orderCount: item.orderCount,
        orderIncome: item.orderIncome,
        licenseCount: 0,
        licenseIncome: 0
      });
    });

    // Add license data
    licensesByPeriod.forEach(item => {
      const key = JSON.stringify(item._id);
      const existing = periodMap.get(key) || {
        period: item._id,
        orderCount: 0,
        orderIncome: 0,
        licenseCount: 0,
        licenseIncome: 0
      };

      existing.licenseCount = item.licenseCount;
      existing.licenseIncome = item.licenseIncome;
      periodMap.set(key, existing);
    });

    // Convert to array and add totals
    const result = Array.from(periodMap.values()).map(item => ({
      ...item,
      totalIncome: item.orderIncome + item.licenseIncome,
      totalTransactions: item.orderCount + item.licenseCount
    }));

    res.json({
      success: true,
      data: result,
      period,
      dateRange: { start, end }
    });
  } catch (error) {
    console.error('Error getting income by period:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get income by period',
      error: error.message
    });
  }
};

/**
 * Get income breakdown by source (orders vs licenses)
 */
const getIncomeBreakdown = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(end.getFullYear(), end.getMonth() - 11, 1);

    // Get order statistics
    const orderStats = await OrderModel.aggregate([
      {
        $match: {
          paid: true,
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: null,
          count: { $sum: 1 },
          totalIncome: { $sum: 20000 }
        }
      }
    ]);

    // Get license statistics
    const licenseStats = await LicenseModel.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: null,
          count: { $sum: 1 },
          totalIncome: { $sum: 5000 }
        }
      }
    ]);

    const orders = orderStats[0] || { count: 0, totalIncome: 0 };
    const licenses = licenseStats[0] || { count: 0, totalIncome: 0 };
    const totalIncome = orders.totalIncome + licenses.totalIncome;

    const breakdown = [
      {
        source: 'orders',
        label: 'Installation Orders',
        count: orders.count,
        income: orders.totalIncome,
        percentage: totalIncome > 0 ? (orders.totalIncome / totalIncome * 100).toFixed(2) : 0,
        unitPrice: 20000
      },
      {
        source: 'licenses',
        label: 'License Subscriptions',
        count: licenses.count,
        income: licenses.totalIncome,
        percentage: totalIncome > 0 ? (licenses.totalIncome / totalIncome * 100).toFixed(2) : 0,
        unitPrice: 5000
      }
    ];

    res.json({
      success: true,
      data: {
        breakdown,
        totalIncome,
        totalTransactions: orders.count + licenses.count,
        dateRange: { start, end }
      }
    });
  } catch (error) {
    console.error('Error getting income breakdown:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get income breakdown',
      error: error.message
    });
  }
};

/**
 * Get income growth statistics (comparing periods)
 */
const getIncomeGrowth = async (req, res) => {
  try {
    const { period = 'monthly' } = req.query;

    const now = new Date();
    let currentStart, currentEnd, previousStart, previousEnd;

    // Define current and previous periods
    switch (period) {
      case 'daily':
        currentStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        currentEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
        previousStart = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1);
        previousEnd = currentStart;
        break;
      case 'yearly':
        currentStart = new Date(now.getFullYear(), 0, 1);
        currentEnd = new Date(now.getFullYear() + 1, 0, 1);
        previousStart = new Date(now.getFullYear() - 1, 0, 1);
        previousEnd = currentStart;
        break;
      default: // monthly
        currentStart = new Date(now.getFullYear(), now.getMonth(), 1);
        currentEnd = new Date(now.getFullYear(), now.getMonth() + 1, 1);
        previousStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        previousEnd = currentStart;
    }

    // Get current period income
    const currentIncome = await Promise.all([
      OrderModel.aggregate([
        {
          $match: {
            paid: true,
            createdAt: { $gte: currentStart, $lt: currentEnd }
          }
        },
        {
          $group: {
            _id: null,
            count: { $sum: 1 },
            income: { $sum: 20000 }
          }
        }
      ]),
      LicenseModel.aggregate([
        {
          $match: {
            createdAt: { $gte: currentStart, $lt: currentEnd }
          }
        },
        {
          $group: {
            _id: null,
            count: { $sum: 1 },
            income: { $sum: 5000 }
          }
        }
      ])
    ]);

    // Get previous period income
    const previousIncome = await Promise.all([
      OrderModel.aggregate([
        {
          $match: {
            paid: true,
            createdAt: { $gte: previousStart, $lt: previousEnd }
          }
        },
        {
          $group: {
            _id: null,
            count: { $sum: 1 },
            income: { $sum: 20000 }
          }
        }
      ]),
      LicenseModel.aggregate([
        {
          $match: {
            createdAt: { $gte: previousStart, $lt: previousEnd }
          }
        },
        {
          $group: {
            _id: null,
            count: { $sum: 1 },
            income: { $sum: 5000 }
          }
        }
      ])
    ]);

    // Calculate totals
    const currentTotal = (currentIncome[0][0]?.income || 0) + (currentIncome[1][0]?.income || 0);
    const previousTotal = (previousIncome[0][0]?.income || 0) + (previousIncome[1][0]?.income || 0);

    const currentTransactions = (currentIncome[0][0]?.count || 0) + (currentIncome[1][0]?.count || 0);
    const previousTransactions = (previousIncome[0][0]?.count || 0) + (previousIncome[1][0]?.count || 0);

    // Calculate growth percentages
    const incomeGrowth = previousTotal > 0 ? ((currentTotal - previousTotal) / previousTotal * 100).toFixed(2) : 0;
    const transactionGrowth = previousTransactions > 0 ? ((currentTransactions - previousTransactions) / previousTransactions * 100).toFixed(2) : 0;

    res.json({
      success: true,
      data: {
        current: {
          income: currentTotal,
          transactions: currentTransactions,
          period: { start: currentStart, end: currentEnd }
        },
        previous: {
          income: previousTotal,
          transactions: previousTransactions,
          period: { start: previousStart, end: previousEnd }
        },
        growth: {
          income: parseFloat(incomeGrowth),
          transactions: parseFloat(transactionGrowth),
          incomeChange: currentTotal - previousTotal,
          transactionChange: currentTransactions - previousTransactions
        },
        period
      }
    });
  } catch (error) {
    console.error('Error getting income growth:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get income growth',
      error: error.message
    });
  }
};

/**
 * Export income data for reporting purposes
 */
const exportIncomeData = async (req, res) => {
  try {
    const { startDate, endDate, format = 'json' } = req.query;

    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(end.getFullYear(), end.getMonth() - 11, 1);

    // Get detailed order data
    const orders = await OrderModel.find({
      paid: true,
      createdAt: { $gte: start, $lte: end }
    }).select('phoneNumber CarModel createdAt invoiceId realInvoiceId').lean();

    // Get detailed license data
    const licenses = await LicenseModel.find({
      createdAt: { $gte: start, $lte: end }
    }).populate('owner', 'phoneNumber username').lean();

    // Format data for export
    const exportData = {
      summary: {
        totalIncome: (orders.length * 20000) + (licenses.length * 5000),
        totalTransactions: orders.length + licenses.length,
        orderIncome: orders.length * 20000,
        licenseIncome: licenses.length * 5000,
        orderCount: orders.length,
        licenseCount: licenses.length,
        dateRange: { start, end }
      },
      orders: orders.map(order => ({
        type: 'order',
        phoneNumber: order.phoneNumber,
        carModel: order.CarModel,
        amount: 20000,
        date: order.createdAt,
        invoiceId: order.invoiceId,
        realInvoiceId: order.realInvoiceId
      })),
      licenses: licenses.map(license => ({
        type: 'license',
        phoneNumber: license.owner?.phoneNumber || 'N/A',
        username: license.owner?.username || 'N/A',
        amount: 5000,
        date: license.createdAt,
        licenseType: license.type || 'monthly'
      }))
    };

    // Combine all transactions for chronological export
    const allTransactions = [
      ...exportData.orders,
      ...exportData.licenses
    ].sort((a, b) => new Date(b.date) - new Date(a.date));

    if (format === 'csv') {
      // Generate CSV format
      const csvHeaders = 'Date,Type,Phone Number,Description,Amount,Invoice ID\n';
      const csvRows = allTransactions.map(transaction => {
        const description = transaction.type === 'order'
          ? `Installation Order - ${transaction.carModel}`
          : `License Subscription - ${transaction.licenseType}`;

        return [
          new Date(transaction.date).toISOString().split('T')[0],
          transaction.type,
          transaction.phoneNumber,
          description,
          transaction.amount,
          transaction.invoiceId || transaction.realInvoiceId || 'N/A'
        ].join(',');
      }).join('\n');

      const csvContent = csvHeaders + csvRows;

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename=income-report-${start.toISOString().split('T')[0]}-to-${end.toISOString().split('T')[0]}.csv`);
      res.send(csvContent);
    } else {
      // Return JSON format
      res.json({
        success: true,
        data: {
          ...exportData,
          allTransactions
        }
      });
    }
  } catch (error) {
    console.error('Error exporting income data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export income data',
      error: error.message
    });
  }
};

module.exports = {
  userList,
  walletRequest,
  walletChange,
  rentCarStatus,
  walletTransactions,
  removeUsers,
  changeActive,
  driverLicenseVerification,
  extendsLicense,
  orderList,
  sendNotifications,
  resetUserPin,
  setUserRole,
  // Statistics endpoints
  getCommandStatistics,
  getTopUsers,
  getDeviceAnalytics,
  getTimeBasedStatistics,
  getDetailedCommandLogs,
  // Development helper
  createSampleData,
  // Command logging
  logCommand,
  // SSL certificate validation
  uploadSslValidationFile,
  listSslValidationFiles,
  deleteSslValidationFile,
  // Income monitoring endpoints
  getIncomeStatistics,
  getIncomeByPeriod,
  getIncomeBreakdown,
  getIncomeGrowth,
  exportIncomeData
};
