const express = require("express");
const router = express.Router();
const ObjectId = require('mongoose').Types.ObjectId;
const licenseModel = require('../../models/license');
const userModel = require('../../models/user');
const Generator = require('license-key-generator');
const { getEbarimt, QPay } = require("../../utils/QPayment");
const INVOICE_VS_REAL_INVOICE_ID = require('../../utils/QPayment').INVOICE_VS_REAL_INVOICE_ID;
const Order = require('../../models/order');
const { sendSms } = require("../../utils/channel");
const wallet = require("../../models/wallet");
const axios = require('axios');

/******************* */

router.post('/ebarimt', getEbarimt);

router.get(
    '/qpay/:page/:invoice/:cost/:sender/:phoneNumber',
    async (req, res) => {

        try {
            console.log(req.params, " is payment hook");

            const { invoice, sender, cost, page, phoneNumber } = req.params;
            if (page == 'license') {
                // Calculate days correctly: cost / price_per_month = months, then * 30 days per month
                const pricePerMonth = parseFloat(process.env.PRICE_PER_MONTH) || 5000;
                const months = parseFloat(cost) / pricePerMonth;
                const days = months * 30; // 30 days per month

                console.log(`Payment processing: cost=${cost}, pricePerMonth=${pricePerMonth}, months=${months}, days=${days}`);

                const user = await userModel.findById(sender);
                if (!user) {
                    console.error('User not found:', sender);
                    return;
                }

                // Check if license already exists for this invoice to prevent duplicates
                const existingLicense = await licenseModel.findOne({ invoice });
                if (existingLicense) {
                    console.log('License already exists for invoice:', invoice);
                    return;
                }

                const options = {
                    type: "random",
                    length: 12,
                    group: 3,
                    split: '-',
                    splitStatus: true
                }
                const code = new Generator(options);

                // Use Promise instead of callback to avoid timing issues
                try {
                    const licenseKey = await new Promise((resolve, reject) => {
                        code.get((err, key) => {
                            if (err) reject(err);
                            else resolve(key);
                        });
                    });

                    console.log('Generated license key:', licenseKey);

                    // Calculate new expiration date
                    let currentExpiry = Date.now();
                    if (user.expired && new Date(user.expired).getTime() > Date.now()) {
                        // If user has valid license, extend from current expiry
                        currentExpiry = new Date(user.expired).getTime();
                    }

                    const newExpiry = currentExpiry + (days * 24 * 60 * 60 * 1000); // days to milliseconds
                    const expiryDate = new Date(newExpiry);

                    console.log(`Expiry calculation: currentExpiry=${new Date(currentExpiry)}, days=${days}, newExpiry=${expiryDate}`);

                    // Create license record
                    const license = new licenseModel({
                        owner: ObjectId(sender),
                        invoice: invoice,
                        cost: parseFloat(cost),
                        licenseKey: licenseKey,
                        expired: expiryDate,
                        realInvoice: INVOICE_VS_REAL_INVOICE_ID[invoice] || '',
                        createdAt: new Date() // Ensure createdAt is set for income tracking
                    });

                    await license.save();
                    console.log('License saved:', license._id);

                    // Update user
                    user.expired = expiryDate;
                    user.licenseKey = licenseKey;
                    user.status = "active";
                    await user.save();
                    console.log('User updated:', user._id);

                    // Send SMS notification
                    try {
                        const sms = {
                            mobile: user.phoneNumber,
                            sms: `sain bn u, tani license amjilttai sungagdlaa www.aslaa.mn`
                        };
                        await sendSms(sms, {}, res);
                    } catch (smsError) {
                        console.error('SMS sending failed:', smsError);
                    }

                } catch (error) {
                    console.error('License creation failed:', error);
                }
            }
            if (page == 'order') {
                const order = await Order.findOne({ phoneNumber });
                if (order) {
                    order.paid = true;
                    order.invoiceId = invoice;
                    order.realInvoiceId = INVOICE_VS_REAL_INVOICE_ID[invoice];
                    await order.save();


                    const sms = {
                        mobile: phoneNumber,
                        sms: ''
                    }
                    sms.sms = `sain bn u, tani ${order.CarModel} mashind asaaltin tohooromj suuriluulah zahialga batalgaajlaa www.aslaa.mn`

                    const response = await sendSms(sms, {}, res);
                }
            }
            if (page == 'balance') {
                const user = await userModel.findById(sender);
                user.balance = (user?.balance || 0) + parseInt(cost);
                await user.save();
                wallet.updateOne(
                    {
                        user: sender,
                    },
                    {
                        $set: { user: sender, currentBalance: user.balance, },
                        $push: { transactions: { ts: Date.now(), mode: 'deposit', description: 'QPay Hook', before: (user.balance - parseInt(cost)), amount: parseInt(cost), invoice, realInvoiceId: INVOICE_VS_REAL_INVOICE_ID[invoice] } }
                    },
                    {
                        upsert: true
                    }
                ).then(res)
            }


        } catch (err) {
            console.log(err);

        }
        res.status(200).json({ success: true });
    }
)


// This route should be accessible at /api/hook/payment/check/:invoiceId
router.get('/check/:invoiceId', async (req, res) => {
    try {
        const { invoiceId } = req.params;
        
        // First get the real invoice ID from our mapping
        const realInvoiceId = INVOICE_VS_REAL_INVOICE_ID[invoiceId];
        
        if (!realInvoiceId) {
            return res.status(404).json({
                success: false,
                message: "Invoice ID not found in our records"
            });
        }

        // Get token
        const token = await QPay().getAuthToken();
        if (!token) {
            return res.status(401).json({
                success: false,
                message: "Could not get QPay token"
            });
        }

        // Check payment using real invoice ID
        const paymentCheck = await QPay().paymentCheck(token, realInvoiceId);
        
        if (paymentCheck.status === 200) {
            return res.json({
                success: true,
                payment: paymentCheck.result,
                invoiceId: invoiceId,
                realInvoiceId: realInvoiceId
            });
        }

        return res.status(404).json({
            success: false,
            message: "Payment not found",
            invoiceId: invoiceId,
            realInvoiceId: realInvoiceId
        });

    } catch (error) {
        console.error("Manual payment check error:", error);
        return res.status(500).json({
            success: false,
            error: error.message,
            invoiceId: req.params.invoiceId
        });
    }
});

// Manual license creation endpoint for when payment is confirmed
router.post('/create-license-manual', async (req, res) => {
    try {
        console.log('Manual license creation request received:', req.body);

        const { userId, cost, invoiceId } = req.body;

        if (!userId || !cost || !invoiceId) {
            console.error('Missing required fields:', { userId, cost, invoiceId });
            return res.status(400).json({
                success: false,
                message: "Missing required fields: userId, cost, invoiceId"
            });
        }

        // Calculate days correctly: cost / price_per_month = months, then * 30 days per month
        const pricePerMonth = parseFloat(process.env.PRICE_PER_MONTH) || 5000;
        const months = parseFloat(cost) / pricePerMonth;
        const days = months * 30; // 30 days per month

        console.log(`Manual license creation: cost=${cost}, pricePerMonth=${pricePerMonth}, months=${months}, days=${days}`);

        const user = await userModel.findById(userId);
        console.log('User found:', user ? `${user.phoneNumber} (${user._id})` : 'Not found');

        if (!user) {
            console.error('User not found for ID:', userId);
            return res.status(404).json({
                success: false,
                message: "User not found"
            });
        }

        // Check if license already exists for this invoice
        const existingLicense = await licenseModel.findOne({ invoice: invoiceId });
        console.log('Existing license check:', existingLicense ? `Found: ${existingLicense._id}` : 'None found');

        if (existingLicense) {
            console.log('License already exists, returning existing license');
            return res.json({
                success: true,
                message: "License already exists for this invoice",
                license: existingLicense
            });
        }

        const options = {
            type: "random",
            length: 12,
            group: 3,
            split: '-',
            splitStatus: true
        }
        const code = new Generator(options);

        const licenseKey = await new Promise((resolve, reject) => {
            code.get((err, key) => {
                if (err) reject(err);
                else resolve(key);
            });
        });

        // Calculate new expiration date correctly
        let currentExpiry = Date.now();
        if (user.expired && new Date(user.expired).getTime() > Date.now()) {
            // If user has valid license, extend from current expiry
            currentExpiry = new Date(user.expired).getTime();
        }

        const newExpiry = currentExpiry + (days * 24 * 60 * 60 * 1000); // days to milliseconds
        const expiryDate = new Date(newExpiry);

        console.log(`Manual expiry calculation: currentExpiry=${new Date(currentExpiry)}, days=${days}, newExpiry=${expiryDate}`);

        // Create license record
        const license = new licenseModel({
            owner: ObjectId(userId),
            invoice: invoiceId,
            cost: parseFloat(cost),
            licenseKey: licenseKey,
            expired: expiryDate,
            realInvoice: INVOICE_VS_REAL_INVOICE_ID[invoiceId] || '',
            createdAt: new Date() // Ensure createdAt is set for income tracking
        });

        await license.save();
        console.log('License saved successfully:', license._id);

        // Update user with new expiry
        const oldExpiry = user.expired;
        user.expired = expiryDate;
        user.licenseKey = licenseKey;
        user.status = "active";
        await user.save();

        console.log('User updated successfully:', {
            userId: user._id,
            oldExpiry: oldExpiry,
            newExpiry: expiryDate,
            licenseKey: licenseKey
        });

        // Send SMS notification
        try {
            const sms = {
                mobile: user.phoneNumber,
                sms: `sain bn u, tani license amjilttai sungagdlaa www.aslaa.mn`
            };
            await sendSms(sms, {}, res);
        } catch (smsError) {
            console.log('SMS sending failed:', smsError);
        }

        res.json({
            success: true,
            message: "License created successfully",
            license: {
                id: license._id,
                licenseKey: license.licenseKey,
                expired: license.expired,
                cost: license.cost
            }
        });

    } catch (error) {
        console.error("Manual license creation error:", error);
        res.status(500).json({
            success: false,
            message: "Failed to create license",
            error: error.message
        });
    }
});

// Test endpoint to verify license creation and income tracking
router.get('/test-license-creation/:userId/:cost', async (req, res) => {
    try {
        const { userId, cost } = req.params;

        // Create a test license
        const testInvoice = `TEST-${Date.now()}`;

        const response = await axios.post(`${req.protocol}://${req.get('host')}/api/hook/create-license-manual`, {
            userId: userId,
            cost: parseFloat(cost),
            invoiceId: testInvoice
        });

        // Check if license was created
        const license = await licenseModel.findOne({ invoice: testInvoice });

        // Check recent licenses for income tracking
        const recentLicenses = await licenseModel.find({
            createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
        }).sort({ createdAt: -1 }).limit(5);

        res.json({
            success: true,
            testResults: {
                licenseCreated: !!license,
                licenseData: license,
                manualCreationResponse: response.data,
                recentLicenses: recentLicenses.length,
                recentLicensesList: recentLicenses.map(l => ({
                    id: l._id,
                    cost: l.cost,
                    createdAt: l.createdAt,
                    invoice: l.invoice
                }))
            }
        });

    } catch (error) {
        console.error('Test license creation error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Debug endpoint to check user license status
router.get('/debug-user-license/:userId', async (req, res) => {
    try {
        const { userId } = req.params;

        // Get user info
        const user = await userModel.findById(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Get recent licenses for this user
        const recentLicenses = await licenseModel.find({
            owner: ObjectId(userId)
        }).sort({ createdAt: -1 }).limit(10);

        // Get all licenses from last 24 hours
        const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);
        const recentAllLicenses = await licenseModel.find({
            createdAt: { $gte: last24Hours }
        }).populate('owner', 'phoneNumber').sort({ createdAt: -1 });

        res.json({
            success: true,
            debug: {
                user: {
                    id: user._id,
                    phoneNumber: user.phoneNumber,
                    currentExpiry: user.expired,
                    licenseKey: user.licenseKey,
                    status: user.status,
                    daysRemaining: user.expired ? Math.ceil((new Date(user.expired) - new Date()) / (1000 * 60 * 60 * 24)) : 0
                },
                userLicenses: recentLicenses.map(l => ({
                    id: l._id,
                    cost: l.cost,
                    expired: l.expired,
                    createdAt: l.createdAt,
                    invoice: l.invoice,
                    licenseKey: l.licenseKey
                })),
                allRecentLicenses: recentAllLicenses.map(l => ({
                    id: l._id,
                    owner: l.owner?.phoneNumber || 'Unknown',
                    cost: l.cost,
                    createdAt: l.createdAt,
                    invoice: l.invoice
                }))
            }
        });

    } catch (error) {
        console.error('Debug user license error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});


module.exports = router;
