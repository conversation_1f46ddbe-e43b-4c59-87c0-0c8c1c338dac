(this.webpackJsonpclient=this.webpackJsonpclient||[]).push([[41,43],{1389:function(e,t,c){"use strict";c.r(t),c.d(t,"default",(function(){return S}));var n=c(43),r=c(49),i=c(669),o=c(529),a=c(668),s=c(670),d=c(1174),l=c(318),j=c(604),u=c(577),b=c(814),h=c(8),x=c(1021),O=c(231),m=c(5),p=c(0),g=c(651),f=c(1024),v=c(679),y=c(1115),w=c(1040),q=c(71),C=c(2);function z(){const e=Object(m.l)(),{otpVerify:t}=Object(q.a)(),{enqueueSnackbar:c}=Object(O.b)(),n=x.b().shape({code1:x.c().required("Code is required"),code2:x.c().required("Code is required"),code3:x.c().required("Code is required"),code4:x.c().required("Code is required"),code5:x.c().required("Code is required"),code6:x.c().required("Code is required")}),{watch:r,control:i,setValue:o,handleSubmit:a,formState:{isSubmitting:s,isValid:d}}=Object(g.f)({mode:"onBlur",resolver:Object(f.a)(n),defaultValues:{code1:"",code2:"",code3:"",code4:"",code5:"",code6:""}}),l=r();Object(p.useEffect)((()=>{document.addEventListener("paste",j)}),[]);const j=e=>{var t;let c=(null===e||void 0===e||null===(t=e.clipboardData)||void 0===t?void 0:t.getData("Text"))||"";c=c.split(""),[].forEach.call(document.querySelectorAll("#field-code"),((e,t)=>{e.value=c[t];const n="code".concat(t+1);o(n,c[t])}))};return Object(C.jsx)(C.Fragment,{children:Object(C.jsxs)("form",{onSubmit:a((async n=>{try{const r=t=>{t.success?(c("Phone verification is success",{variant:"success"}),e("/")):c("Your phonenumber is not registered",{variant:"error"})};t(Object.values(n).join(""),r)}catch(r){console.error(r)}})),children:[Object(C.jsx)(v.a,{direction:"row",spacing:3,mb:3,justifyContent:"center",children:Object.keys(l).map(((e,t)=>Object(C.jsx)(g.a,{name:"code".concat(t+1),control:i,render:e=>{let{field:c}=e;return Object(C.jsx)(y.c,Object(h.a)(Object(h.a)({},c),{},{id:"field-code",autoFocus:0===t,placeholder:"-",onChange:e=>((e,t)=>{const{maxLength:c,value:n,name:r}=e.target,i=r.replace("code",""),o=Number(i);if(n.length>=c&&o<6){const e=document.querySelector("input[name=code".concat(o+1,"]"));null!==e&&e.focus()}t(e)})(e,c.onChange),inputProps:{maxLength:1,sx:{py:.5,textAlign:"center",width:{xs:36,sm:65},height:{xs:43,sm:80}}},sx:{fontSize:"2.3rem",bgcolor:"grey.50016",borderRadius:1}}))}},e)))}),Object(C.jsx)(w.a,{size:"large",type:"submit",variant:"contained",loading:s,disabled:!d,sx:{bgcolor:"grey.50016",border:"1px solid",borderColor:"grey.50048"},children:"Next"})]})})}const k=Object(r.a)("div")((e=>{let{theme:t}=e;return{display:"flex",alignItems:"center",padding:t.spacing(8,0),height:"100vh"}}));function S(){return Object(C.jsx)(j.a,{title:"Verify",sx:{height:1},children:Object(C.jsxs)(k,{children:[Object(C.jsx)(l.a,{}),Object(C.jsxs)(i.a,{children:[Object(C.jsxs)(o.a,{sx:{maxWidth:480,mx:"auto",textAlign:"center"},children:[Object(C.jsx)(a.a,{size:"small",component:n.b,to:"/auth/login",startIcon:Object(C.jsx)(u.a,{icon:"eva:arrow-ios-back-fill",width:20,height:20}),sx:{color:"text.primary"},children:"Back"}),Object(C.jsx)(o.a,{width:"50%",sx:{mx:"auto",mb:3},children:Object(C.jsx)(b.default,{})}),Object(C.jsx)(s.a,{variant:"h3",paragraph:!0,children:"Verification Code"}),Object(C.jsx)(s.a,{paragraph:!0,children:"Please enter cerification code we sent to your phone number"}),Object(C.jsx)(o.a,{sx:{mt:5,mb:3},children:Object(C.jsx)(z,{})}),Object(C.jsxs)(s.a,{variant:"body2",children:["Haven't got your OTP number yet?  \xa0",Object(C.jsx)(d.a,{variant:"subtitle2",underline:"none",sx:{cursor:"pointer",color:"text.secondary"},onClick:()=>{},children:"Resend code"})]})]}),Object(C.jsx)("div",{id:"recaptcha-container"})]})]})})}},577:function(e,t,c){"use strict";c.d(t,"a",(function(){return d}));var n=c(8),r=c(571),i=c(606),o=c(529),a=c(2);const s=["icon","sx"];function d(e){let{icon:t,sx:c}=e,d=Object(r.a)(e,s);return Object(a.jsx)(o.a,Object(n.a)({component:i.a,icon:t,sx:Object(n.a)({},c)},d))}},604:function(e,t,c){"use strict";var n=c(8),r=c(571),i=c(6),o=c.n(i),a=c(234),s=c(0),d=c(529),l=c(669),j=c(2);const u=["children","title","meta"],b=Object(s.forwardRef)(((e,t)=>{let{children:c,title:i="",meta:o}=e,s=Object(r.a)(e,u);return Object(j.jsxs)(j.Fragment,{children:[Object(j.jsxs)(a.a,{children:[Object(j.jsx)("title",{children:i}),o]}),Object(j.jsx)(d.a,Object(n.a)(Object(n.a)({ref:t},s),{},{children:Object(j.jsx)(l.a,{children:c})}))]})}));b.propTypes={children:o.a.node.isRequired,title:o.a.string,meta:o.a.node},t.a=b},814:function(e,t,c){"use strict";c.r(t),c.d(t,"default",(function(){return s}));var n=c(8),r=c(43),i=c(124),o=c(529),a=c(2);function s(e){let{disabledLink:t=!1,sx:c,color:s}=e;const d=Object(i.a)(),l=void 0!==s?s:d.palette.grey[50048],j=Object(a.jsx)(o.a,{sx:Object(n.a)({width:"inherit",height:"inherit"},c),children:Object(a.jsx)("svg",{version:"1.0",xmlns:"http://www.w3.org/2000/svg",width:"100%",height:"100%",viewBox:"0 0 220.000000 180.000000",preserveAspectRatio:"xMidYMid meet",children:Object(a.jsx)("g",{transform:"translate(0.000000,229.000000) scale(0.100000,-0.100000)",fill:l,stroke:"none",children:Object(a.jsx)("path",{d:"M714 1820 c-29 -4 -58 -11 -65 -16 -43 -25 -89 -69 -158 -150 l-78\n-91 -11 30 -11 30 -72 -6 c-149 -13 -160 -82 -18 -121 32 -10 59 -19 59 -21 0\n-2 -20 -13 -44 -25 -55 -26 -121 -96 -149 -158 -20 -43 -22 -66 -25 -272 -4\n-253 -1 -282 34 -317 17 -17 24 -35 24 -64 0 -29 7 -47 25 -64 21 -22 33 -25\n93 -25 86 0 111 16 119 78 l6 42 658 0 659 0 0 -25 c0 -33 25 -81 45 -89 9 -3\n47 -6 84 -6 83 0 111 22 111 87 0 32 7 48 30 73 l31 33 -3 256 c-3 244 -4 258\n-26 303 -30 60 -89 121 -147 151 l-46 23 58 18 c77 24 103 41 103 70 0 28 -27\n43 -101 54 -66 10 -99 1 -99 -28 0 -11 -3 -20 -8 -20 -4 0 -44 42 -88 93 -100\n115 -148 149 -223 158 -74 10 -702 9 -767 -1z m787 -60 c40 -11 127 -97 213\n-209 l50 -64 -49 6 c-211 29 -962 34 -1174 7 -46 -6 -86 -8 -89 -5 -12 12 180\n235 222 257 12 6 59 15 106 19 120 11 677 3 721 -11z m-147 -321 c28 -22 96\n-136 96 -161 0 -9 -7 -19 -16 -22 -9 -3 -161 -6 -339 -6 -378 0 -367 -3 -319\n87 16 30 43 71 60 89 l31 34 230 0 c217 0 232 -1 257 -21z m-952 -208 c84 -23\n159 -48 176 -61 32 -24 47 -59 32 -74 -4 -4 -90 -7 -189 -4 -216 5 -221 7\n-221 99 0 45 4 60 18 68 24 14 21 15 184 -28z m1596 9 c17 -34 8 -98 -18 -124\n-19 -20 -33 -21 -205 -24 -171 -4 -185 -3 -192 14 -5 13 4 27 35 54 36 29 65\n41 185 72 78 20 151 36 162 35 11 -1 25 -13 33 -27z m-1352 -288 c13 -8 84\n-146 84 -162 0 -11 -129 -14 -146 -2 -17 12 -103 156 -98 164 6 10 145 10 160\n0z m834 -9 c0 -10 -17 -49 -38 -88 l-37 -70 -295 -2 c-162 -2 -300 0 -306 5\n-13 8 -84 146 -84 162 0 7 127 10 380 10 355 0 380 -1 380 -17z m240 7 c0 -13\n-89 -153 -104 -162 -16 -11 -134 -10 -141 2 -6 10 48 124 73 153 12 13 31 17\n94 17 45 0 78 -4 78 -10z"})})})});return t?Object(a.jsx)(a.Fragment,{children:j}):Object(a.jsx)(r.b,{to:"/",children:j})}}}]);
//# sourceMappingURL=41.1d64c047.chunk.js.map